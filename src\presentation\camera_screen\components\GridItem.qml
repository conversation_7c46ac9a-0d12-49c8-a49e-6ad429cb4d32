import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0

Rectangle {
    id: root
    color: "transparent"// isDarkTheme   ? "#1a1a1a" : "#f0f0f0"
    border.color: "transparent"
    border.width: isSelected ? 2 : 1
    visible: opacity > 0
    opacity: gridModel && gridModel.isMaximized ? (gridModel.activeItemPosition === position ? 1 : 0) : 1
    z: calculateZ()


    // Listen for cell dimension changes
    Connections {
        target: gridModel
        function onCellDimensionsChanged(pos, width, height) {
            if (pos === position) {
                var cellWidth = root.parent.width / gridModel.columns;
                var cellHeight = root.parent.height / gridModel.rows;
                root.width = width * cellWidth;
                root.height = height * cellHeight;
            }
        }
    }
    // Connections {
    //     target: gridModel.gridItemSelected
    //     function onWidgetChanged() {
    //         console.log("onWidgetChanged = ")
    //     }
    // }
    // Theme properties - get from gridModel if available
    property string menuBackgroundColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    property string menuTextColor: isDarkTheme ? "#ffffff" : "#2b2a3a"
    property string menuHoverColor: isDarkTheme ? "#3f3f87" : "#f0f0f0"
    property string menuSeparatorColor: isDarkTheme ? "#656475" : "#cdcdcd"
    property string menuBorderColor: isDarkTheme ? "#656475" : "#cdcdcd"

    // Properties for storing original position and size
    property real normalX: x
    property real normalY: y
    property real normalWidth: width
    property real normalHeight: height
    property real startWidth: 0
    property real startHeight: 0
    property real startX: 0
    property real startY: 0
    property bool isSwapping: false
    property bool dragActive: false
    property var itemData: null
    // Listen for changes in CameraModel
    Connections {
        target: itemData
        function onStateChanged() {
            console.log("Camera state changed:", itemData.state_merged, "for camera:", itemData.name)
            // Force update of camera state icon and name
            if (cameraStateIcon) {
                cameraStateIcon.source = cameraStateIcon.getCameraStateIcon()
            }
            if (cameraNameText) {
                cameraNameText.text = cameraNameText.getCameraName()
            }
        }
        function onStateMergeChanged() {
            console.log("Camera state_merged changed:", itemData.state_merged, "for camera:", itemData.name)
            // Force update of camera state icon
            if (cameraStateIcon) {
                cameraStateIcon.source = cameraStateIcon.getCameraStateIcon()
            }
        }
        function onNameChanged() {
            console.log("Camera name changed:", itemData.name)
            // Force update of camera name text
            if (cameraNameText) {
                cameraNameText.text = cameraNameText.getCameraName()
            }
        }
    }

    // Drag and drop properties
    Drag.active: dragArea.drag.active && !(gridModel && gridModel.isMaximized) // Không cho phép kéo thả khi ở chế độ fullscreen
    Drag.hotSpot.x: width / 2
    Drag.hotSpot.y: height / 2
    Drag.source: root
    Drag.keys: ["videoItem"]

    // Resize areas
    Rectangle {
        id: rightResizer
        width: 4
        height: parent.height
        color: mouseArea.containsMouse || mouseArea.pressed ? (isDarkTheme ? "transparent" : "transparent") : "transparent"
        anchors.right: parent.right
        z: 10

        MouseArea {
            id: mouseArea
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeHorCursor
            property real lastMouseX: 0

            onPressed: {
                lastMouseX = mouseX
                startWidth = root.width
            }

            onPositionChanged: {
                if (pressed) {
                    var delta = mouseX - lastMouseX
                    var cellWidth = root.parent.width / gridModel.columns
                    var newWidth = Math.max(cellWidth, root.width + delta)
                    var newWidthCells = Math.round(newWidth / cellWidth)
                    var currentDimensions = gridModel.getCellDimensions(position)

                    // Chỉ thay đổi kích thước nếu không có xung đột - sử dụng Python method
                    if (gridModel.canResizeToSize(position, newWidthCells, currentDimensions.height)) {
                        root.width = newWidthCells * cellWidth
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật - sử dụng Python method
                if (gridModel.canResizeToSize(position, widthCells, heightCells)) {
                    // Đặt isSave = false trước khi thay đổi kích thước
                    if (gridModel) {
                        gridModel.isSave = false
                    }
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    Rectangle {
        id: bottomResizer
        width: parent.width
        height: 4
        color: mouseArea2.containsMouse || mouseArea2.pressed ? (isDarkTheme ? "transparent" : "transparent") : "transparent"
        anchors.bottom: parent.bottom
        z: 10

        MouseArea {
            id: mouseArea2
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeVerCursor
            property real lastMouseY: 0

            onPressed: {
                lastMouseY = mouseY
                startHeight = root.height
            }

            onPositionChanged: {
                if (pressed) {
                    var delta = mouseY - lastMouseY
                    var cellHeight = root.parent.height / gridModel.rows
                    var newHeight = Math.max(cellHeight, root.height + delta)
                    var newHeightCells = Math.round(newHeight / cellHeight)
                    var currentDimensions = gridModel.getCellDimensions(position)

                    // Chỉ thay đổi kích thước nếu không có xung đột - sử dụng Python method
                    if (gridModel.canResizeToSize(position, currentDimensions.width, newHeightCells)) {
                        root.height = newHeightCells * cellHeight
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật - sử dụng Python method
                if (gridModel.canResizeToSize(position, widthCells, heightCells)) {
                    // Đặt isSave = false trước khi thay đổi kích thước
                    if (gridModel) {
                        gridModel.isSave = false
                    }
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    Rectangle {
        id: bottomRightResizer
        width: 8
        height: 8
        color: mouseArea3.containsMouse || mouseArea3.pressed ? (isDarkTheme ? "transparent" : "transparent") : "transparent"
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        z: 10

        MouseArea {
            id: mouseArea3
            anchors.fill: parent
            anchors.margins: -4 // Tăng vùng phát hiện chuột
            hoverEnabled: true
            cursorShape: Qt.SizeFDiagCursor
            property real lastMouseX: 0
            property real lastMouseY: 0

            onPressed: {
                lastMouseX = mouseX
                lastMouseY = mouseY
                startWidth = root.width
                startHeight = root.height
            }

            onPositionChanged: {
                if (pressed) {
                    var deltaX = mouseX - lastMouseX
                    var deltaY = mouseY - lastMouseY

                    var cellWidth = root.parent.width / gridModel.columns
                    var cellHeight = root.parent.height / gridModel.rows

                    var newWidth = Math.max(cellWidth, root.width + deltaX)
                    var newHeight = Math.max(cellHeight, root.height + deltaY)

                    var newWidthCells = Math.round(newWidth / cellWidth)
                    var newHeightCells = Math.round(newHeight / cellHeight)

                    // Chỉ thay đổi kích thước nếu không có xung đột - sử dụng Python method
                    if (gridModel.canResizeToSize(position, newWidthCells, newHeightCells)) {
                        root.width = newWidthCells * cellWidth
                        root.height = newHeightCells * cellHeight
                    }
                }
            }

            onReleased: {
                var cellWidth = root.parent.width / gridModel.columns
                var cellHeight = root.parent.height / gridModel.rows
                var widthCells = Math.round(root.width / cellWidth)
                var heightCells = Math.round(root.height / cellHeight)

                // Kiểm tra lại một lần nữa trước khi cập nhật - sử dụng Python method
                if (gridModel.canResizeToSize(position, widthCells, heightCells)) {
                    // Đặt isSave = false trước khi thay đổi kích thước
                    if (gridModel) {
                        gridModel.isSave = false
                    }
                    gridModel.updateCellDimensions(position, widthCells, heightCells)
                } else {
                    // Nếu không thể resize, trở về kích thước cũ
                    var currentDimensions = gridModel.getCellDimensions(position)
                    root.width = currentDimensions.width * cellWidth
                    root.height = currentDimensions.height * cellHeight
                }
            }
        }
    }

    // Functions
    function calculateZ() {
        if (gridModel && gridModel.isMaximized) {
            return (position === gridModel.activeItemPosition) ? 3000 : -1;
        } else if (isMaximizing || isResizing) {
            return 2000;
        } else if (dragActive || isSwapping) {
            return 1000;
        } else {
            return (camera_id !== "") ? 1 : -1;
        }
    }
    function updatePosition() {
        var cellWidth = root.parent.width / gridModel.columns
        var cellHeight = root.parent.height / gridModel.rows
        var col = Math.floor(x / cellWidth)
        var row = Math.floor(y / cellHeight)
        var newPos = row * gridModel.columns + col

        if (newPos !== position && newPos >= 0 && newPos < gridModel.columns * gridModel.rows) {
            position = newPos
            x = col * cellWidth
            y = row * cellHeight
        }
    }
    function startResize(type, mouseX, mouseY) {
        if (type === "width") {
            startWidth = root.width;
            startX = mouseX;
        } else if (type === "height") {
            startHeight = root.height;
            startY = mouseY;
        } else {
            startWidth = root.width;
            startHeight = root.height;
            startX = mouseX;
            startY = mouseY;
        }
    }

    function resize(type, mouseX, mouseY) {
        if (type === "width") {
            var delta = mouseX - startX;
            var newWidth = startWidth + delta;
            var newColumns = Math.ceil(newWidth / (root.parent.width / gridModel.columns));
            root.width = newColumns * (root.parent.width / gridModel.columns);
        } else if (type === "height") {
            var delta = mouseY - startY;
            var newHeight = startHeight + delta;
            var newRows = Math.ceil(newHeight / (root.parent.height / gridModel.rows));
            root.height = newRows * (root.parent.height / gridModel.rows);
        } else {
            var deltaX = mouseX - startX;
            var deltaY = mouseY - startY;
            var newColumns = Math.ceil((startWidth + deltaX) / (root.parent.width / gridModel.columns));
            var newRows = Math.ceil((startHeight + deltaY) / (root.parent.height / gridModel.rows));
            root.width = newColumns * (root.parent.width / gridModel.columns);
            root.height = newRows * (root.parent.height / gridModel.rows);
        }
    }
    function onGridItemChanged(){
        console.log("onGridItemChanged ",root.camera_id)
        videoFrame.isSelected = false
        border.color = defaultBorderColor

        // Tắt trạng thái PTZ khi chuyển sang GridItem khác
        if (isPtzActive || isPtz3dActive) {
            console.log("Deactivating PTZ controls when switching to another GridItem (from onGridItemChanged)")
            activatePtzButton("none")
        }
    }
    // Component.onDestruction: {
    //     if (gridModel && gridModel.gridItemSelected && gridModel.gridItemSelected.widget === root){
    //         videoFrame.isSelected = false
    //         gridModel.gridItemSelected.widget = null

    //     }
    //     videoFrame.isPlaying = false
    // }

    // Properties
    property bool isDarkTheme: true
    property string camera_id: ""
    property bool isPlaying: false
    property int position: 0
    property bool isDragging: false
    property bool isHovered: false
    property bool isSelected: false
    property bool isMaximizing: false
    property bool isResizing: false

    // Digital Zoom properties
    property real zoomFactor: 1.0
    property real minZoom: 1.0
    property real maxZoom: 5.0
    property real zoomSensitivity: 0.1
    property bool isZooming: false
    property point zoomCenter: Qt.point(0, 0)

    // PTZ Zoom properties
    property bool isPTZZooming: false
    property string currentZoomDirection: ""
    property real ptzZoomSpeed: 1// Tốc độ zoom PTZ, có thể điều chỉnh

    // PTZ properties
    property bool isPtz3dActive: false
    property bool isPtzActive: false
    property bool isDragZoomActive: false
    property bool supportsPTZ: false // Whether the camera supports PTZ functionality

    // Hàm để chọn camera hiện tại khi nhấn vào nút PTZ
    function selectThisCamera() {
        try {
            if (!gridModel) {
                console.warn("selectThisCamera: gridModel is null")
                return
            }

            if (gridModel.gridItemSelected && gridModel.gridItemSelected.widget !== root) {
                console.log("Selecting camera");

                // Tắt trạng thái PTZ của camera đang được chọn trước đó
                if (gridModel.gridItemSelected.widget) {
                    if (typeof gridModel.gridItemSelected.widget.onGridItemChanged === "function") {
                        gridModel.gridItemSelected.widget.onGridItemChanged();
                    }
                }

                // Chọn camera này
                if (videoFrame) {
                    videoFrame.isSelected = true;
                }
                if (border) {
                    border.color = hoverBorderColor;
                }

                // Cập nhật widget được chọn
                gridModel.gridItemSelected.widget = root;
            }
        } catch (e) {
            console.error("Error in selectThisCamera:", e)
        }
    }

    // Hàm để đảm bảo chỉ có một nút PTZ được active tại một thời điểm
    function activatePtzButton(buttonType) {
        // Tắt tất cả các nút PTZ ở các GridItem khác trước khi kích hoạt nút PTZ ở GridItem này
        if (buttonType !== "none" && root.parent && root.parent.activeItems) {
            // Duyệt qua tất cả các GridItem trong grid
            for (var pos in root.parent.activeItems) {
                var item = root.parent.activeItems[pos];
                // Bỏ qua GridItem hiện tại
                if (item && item !== root) {
                    // Kiểm tra xem GridItem có phương thức activatePtzButton không
                    if (typeof item.activatePtzButton === "function") {
                        // Tắt tất cả các nút PTZ ở GridItem khác
                        console.log("Deactivating PTZ controls in other GridItem at position:", pos);
                        item.activatePtzButton("none");
                    }
                }
            }
        }

        // Kích hoạt nút PTZ ở GridItem này
        if (buttonType === "ptz") {
            isPtzActive = true;
            isPtz3dActive = false;
            isDragZoomActive = false;
            ptz3dControl.visible = false;
            dragZoomControl.visible = false;

            // Cập nhật vị trí của PTZControlPanel trước khi hiển thị
            ptzControlPanel.x = root.x + root.width + 10;
            ptzControlPanel.y = root.y + controlButtonsRow.y + controlButtonsRow.height + 10;

            // Đảm bảo panel không vượt quá biên phải của màn hình
            if (ptzControlPanel.x + ptzControlPanel.width > root.parent.width) {
                ptzControlPanel.x = root.parent.width - ptzControlPanel.width - 10;
            }

            // Hiển thị panel
            ptzControlPanel.visible = true;
        } else if (buttonType === "ptz3d") {
            isPtzActive = false;
            isPtz3dActive = true;
            isDragZoomActive = false;
            ptz3dControl.visible = true;
            ptzControlPanel.visible = false;
            dragZoomControl.visible = false;
        } else if (buttonType === "dragZoom") {
            isPtzActive = false;
            isPtz3dActive = false;
            isDragZoomActive = true;
            ptz3dControl.visible = false;
            ptzControlPanel.visible = false;
            dragZoomControl.visible = true;
        } else {
            isPtzActive = false;
            isPtz3dActive = false;
            isDragZoomActive = false;
            ptz3dControl.visible = false;
            ptzControlPanel.visible = false;
            dragZoomControl.visible = false;
        }
    }

    // Thuộc tính để kiểm soát thời gian giữa các lần kéo thả
    property int lastDragTime: 0  // Thời gian của lần kéo cuối cùng
    property int dragCooldown: 200  // Thời gian tối thiểu giữa 2 lần kéo (ms)

    // Thêm thuộc tính để lưu kích thước camera khi kéo
    property var dragDimensions: gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}

    // Lắng nghe sự kiện thay đổi kích thước từ GridModel
    Connections {
        target: gridModel
        function onCellDimensionsChanged(pos, width, height) {
            if (pos === position) {
                var cellWidth = root.parent.width / gridModel.columns;
                var cellHeight = root.parent.height / gridModel.rows;
                root.width = width * cellWidth;
                root.height = height * cellHeight;
            }
        }

        // Lắng nghe tín hiệu restoreAnimationStarted từ grid_manager.py
        function onRestoreAnimationStarted(activePosition) {
            console.log("Received restoreAnimationStarted signal for position:", activePosition)
            if (position === activePosition) {
                console.log("This is the active item, starting restore animation with high z-index")
                // Đặt z-index lớn nhất cho animation
                root.z = 5000
                // Bắt đầu animation restore
                restoreAnimation.start()
            }
        }
    }

    property var aiSolutions: null
    property var availableTabs: null

    // Border color handling
    property string defaultBorderColor: "transparent"
    property string hoverBorderColor: isDarkTheme ? "#4fd1c5" : "#4fd1c5"
    property string dragBorderColor: "#FF6B6B"

    onIsHoveredChanged: {
        // if (isHovered && camera_id !== "") {
        //     border.color = hoverBorderColor
        // } else {
        //     border.color = defaultBorderColor
        // }
    }

    // Thêm thuộc tính để theo dõi vị trí ban đầu và trạng thái drop
    property bool dropSuccessful: false

    // Update stored normal values when grid changes (but not during maximize)
    onXChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalX = x;
        // Cập nhật vị trí của PTZControlPanel khi vị trí của GridItem thay đổi
        if (isPtzActive && ptzControlPanel.visible) {
            // Cập nhật vị trí của PTZControlPanel
            ptzControlPanel.x = root.x + root.width + 10;

            // Đảm bảo panel không vượt quá biên phải của màn hình
            if (ptzControlPanel.x + ptzControlPanel.width > root.parent.width) {
                ptzControlPanel.x = root.parent.width - ptzControlPanel.width - 10;
            }
        }
    }
    onYChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalY = y;
        // Cập nhật vị trí của PTZControlPanel khi vị trí của GridItem thay đổi
        if (isPtzActive && ptzControlPanel.visible) {
            ptzControlPanel.y = root.y + controlButtonsRow.y + controlButtonsRow.height + 10;
        }
    }
    onWidthChanged: {
        if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalWidth = width;
        // Cập nhật vị trí của PTZControlPanel khi kích thước của GridItem thay đổi
        if (isPtzActive && ptzControlPanel.visible) {
            ptzControlPanel.x = root.x + root.width + 10;

            // Đảm bảo panel không vượt quá biên phải của màn hình
            if (ptzControlPanel.x + ptzControlPanel.width > root.parent.width) {
                ptzControlPanel.x = root.parent.width - ptzControlPanel.width - 10;
            }
        }
    }
    onHeightChanged: if (gridModel && !gridModel.isMaximized && !isSwapping && !dragActive) normalHeight = height

    // Bind position and size based on maximized state
    x: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? 0 : normalX
    y: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? 0 : normalY
    width: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? parent.width : normalWidth
    height: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ? parent.height : normalHeight

    Behavior on opacity {
        NumberAnimation { duration: 200 }
    }

    Behavior on x {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on y {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on width {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    Behavior on height {
        NumberAnimation {
            duration: 300
            easing.type: Easing.InOutQuad
        }
    }

    // Hover effect and drag area
    MouseArea {
        id: dragArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton
        drag.target: root.camera_id !== "" && gridModel && !gridModel.isMaximized ? parent : undefined // Không cho phép kéo thả khi ở chế độ fullscreen
        drag.smoothed: true
        drag.filterChildren: true
        z: 2

        property real dragThreshold: 10
        property real pressX: 0
        property real pressY: 0
        property bool isDragging: false
        property bool isClick: false

        onPressed: function(mouse) {
            // Không tắt các panel PTZ khi nhấn vào GridItem
            // root.activatePtzButton("none");

            console.log("GridItem pressed, camera_id:", root.camera_id, CommonEnum.MAIN_STREAM)

            try {
                forceActiveFocus() // Đảm bảo MouseArea nhận được focus

                // Kiểm tra null safety trước khi truy cập
                if (!gridModel) {
                    console.warn("gridModel is null")
                    return
                }

                // kịch bản ng dùng focus vào item
                if(gridModel.gridItemSelected && gridModel.gridItemSelected.widget && gridModel.gridItemSelected.widget !== root){
                    if (typeof gridModel.gridItemSelected.widget.onGridItemChanged === "function") {
                        gridModel.gridItemSelected.widget.onGridItemChanged()
                    }
                }

                if (videoFrame) {
                    videoFrame.isSelected = true
                }
                if (border) {
                    border.color = hoverBorderColor
                }

                // Cập nhật vị trí của GridItem trước khi đặt widget
                if (gridModel) {
                    // Đảm bảo vị trí được cập nhật
                    var col = position % gridModel.columns
                    var row = Math.floor(position / gridModel.columns)

                    // Lấy kích thước hiện tại của camera từ GridModel
                    var dimensions = gridModel.getCellDimensions(position)
                    var widthCells = dimensions.width
                    var heightCells = dimensions.height

                    // Cập nhật vị trí và kích thước nếu cần
                    if (!gridModel.isMaximized || position !== gridModel.activeItemPosition) {
                        x = (col * parent.width / gridModel.columns)
                        y = (row * parent.height / gridModel.rows)
                        width = (parent.width / gridModel.columns) * widthCells
                        height = (parent.height / gridModel.rows) * heightCells
                    }

                    console.log("Updated GridItem position on press: x=" + x + ", y=" + y)
                }

                if (gridModel && gridModel.gridItemSelected) {
                    gridModel.gridItemSelected.widget = root
                }
            } catch (e) {
                console.error("Error in onPressed:", e)
                return // Exit early on error to prevent further issues
            }

            if (root.camera_id !== "") {
                // Kiểm tra thời gian giữa các lần nhấn
                var currentTime = Date.now()
                var timeSinceLastDrag = currentTime - (root.lastDragTime || 0)

                // Nếu thời gian giữa các lần nhấn quá ngắn, có thể gây ra lệch tọa độ
                if (timeSinceLastDrag < root.dragCooldown) {
                    console.log("Press ignored - too soon after last drag:", timeSinceLastDrag, "ms")
                    return
                }

                // Lưu tọa độ chuột tương đối với MouseArea
                pressX = mouse.x
                pressY = mouse.y

                // Lưu vị trí thực tế của GridItem (tương đối với parent)
                // Sử dụng root thay vì parent để đảm bảo tính chính xác
                startX = root.x
                startY = root.y

                isClick = true

                // Lưu lại kích thước hiện tại của GridItem
                startWidth = root.width
                startHeight = root.height

                console.log("Press recorded at:", mouse.x, mouse.y, "GridItem position:", root.x, root.y)

                // Chỉ chọn camera khi nhấn Ctrl + chuột trái
                if (mouse.modifiers & Qt.ControlModifier) {
                    // Nếu nhấn Ctrl, đảo trạng thái chọn
                    root.isSelected = !root.isSelected
                    console.log("Camera selection toggled with Ctrl:", root.isSelected)

                }
            }
        }

        onDoubleClicked: function(mouse) {
            // console.log("Double-click detected on position:", position, "isMaximizing:", isMaximizing, "isResizing:", isResizing, "dragActive:", dragActive);

            // Reset dragActive on double-click to prevent issues
            if (dragActive) {
                // console.log("Resetting dragActive on double-click");
                dragActive = false
                root.opacity = 1
                resetDragTimer.stop()
            }

            if (root.camera_id !== "") {
                // Prevent double-click during animation
                if (isMaximizing || isResizing) {
                    // console.log("Ignoring double-click - animation in progress");
                    return;
                }

                // Prevent double-click when PTZ 3D Area is active
                if (isPtz3dActive) {
                    console.log("Ignoring double-click - PTZ 3D Area is active");
                    return;
                }

                // Simple toggle logic - if maximized, restore; if not maximized, maximize
                    if (gridModel && gridModel.isMaximized) {
                        if (position === gridModel.activeItemPosition) {
                            // Đặt isSave = false trước khi restore grid
                            if (gridModel) {
                                gridModel.isSave = false
                            }

                            // Emit signal to grid manager instead of direct handling
                            gridModel.toggleFullscreenRequested(position)
                        }
                    } else if (gridModel) {
                        // Đặt isSave = false trước khi maximize grid
                        if (gridModel) {
                            gridModel.isSave = false
                        }

                        isMaximizing = true // Set z-index cao nhất trước
                        maximizeAnimation.start()
                        // Emit signal to grid manager instead of direct handling
                        gridModel.toggleFullscreenRequested(position)
                    }
            }
        }

        onPositionChanged: function(mouse) {
            if (root.camera_id !== "" && isClick && mouse.buttons & Qt.LeftButton) {
                var dx = mouse.x - pressX
                var dy = mouse.y - pressY
                var distance = Math.sqrt(dx * dx + dy * dy)

                if (distance > dragThreshold && !isDragging && !(gridModel && gridModel.isMaximized)) {
                    isDragging = true
                    isClick = false
                    dragActive = true
                    root.opacity = 0.8

                    // Đặt z-index lớn nhất khi đang kéo
                    root.z = 5000
                    console.log("Dragging started, z-index set to:", root.z)

                    // Thêm thông tin về kích thước camera vào dữ liệu kéo
                    var dimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}

                    // Cập nhật thuộc tính dragDimensions
                    root.dragDimensions = dimensions

                    // Thiết lập mimeData
                    root.Drag.mimeData = {
                        "text/plain": camera_id,
                        "camera/position": position.toString(),
                        "camera/width": dimensions.width.toString(),
                        "camera/height": dimensions.height.toString(),
                        "camera/isFullscreen": (gridModel && gridModel.isMaximized).toString() // Thêm thông tin về trạng thái fullscreen
                    }
                }
            }
        }

        onReleased: function(mouse) {
            try {
                // Cập nhật thời gian kéo cuối cùng khi thả chuột
                root.lastDragTime = Date.now()

                if (mouse.button === Qt.RightButton && root.camera_id !== "") {
                    console.log("Right click detected, camera_id:", root.camera_id)
                    contextMenu.popup()
                    return
                }

                isDragging = false
                isClick = false
            if (dragActive) {
                dragActive = false
                isSwapping = true
                root.opacity = 1

                // Đặt lại z-index về giá trị bình thường
                root.z = calculateZ()
                console.log("Dragging ended, z-index reset to:", root.z)

                // Lấy kích thước hiện tại của item
                var currentDimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                var widthCells = currentDimensions.width
                var heightCells = currentDimensions.height

                // Tính toán vị trí grid dựa trên tọa độ thả
                var dropX = root.x + width / 2
                var dropY = root.y + height / 2

                // Tìm item tại vị trí thả
                var dropItem = null
                var items = root.parent.activeItems || {}

                for (var pos in items) {
                    var item = items[pos]
                    if (item && item !== root) {
                        if (dropX >= item.x && dropX <= item.x + item.width &&
                            dropY >= item.y && dropY <= item.y + item.height) {
                            dropItem = item
                            break
                        }
                    }
                }

                // Tính toán vị trí ô trong grid
                var gridCol = gridModel ? Math.floor(dropX / (root.parent.width / gridModel.columns)) : 0
                var gridRow = gridModel ? Math.floor(dropY / (root.parent.height / gridModel.rows)) : 0

                // Điều chỉnh vị trí để giữ tâm của camera
                // Tính toán offset từ tâm của camera đến góc trên bên trái
                var offsetCol = Math.floor(widthCells / 2)
                var offsetRow = Math.floor(heightCells / 2)

                // Điều chỉnh vị trí để giữ tâm của camera
                var adjustedGridCol = Math.max(0, gridCol - offsetCol)
                var adjustedGridRow = Math.max(0, gridRow - offsetRow)

                // Đảm bảo không vượt quá biên phải và biên dưới
                adjustedGridCol = gridModel ? Math.min(adjustedGridCol, gridModel.columns - widthCells) : 0
                adjustedGridRow = gridModel ? Math.min(adjustedGridRow, gridModel.rows - heightCells) : 0

                // Tính vị trí mới trong grid
                var targetPos = gridModel ? adjustedGridRow * gridModel.columns + adjustedGridCol : 0

                // console.log("Dropping camera with dimensions: " + widthCells + "x" + heightCells +
                //           " at grid position: " + targetPos + " (col: " + adjustedGridCol + ", row: " + adjustedGridRow + ")")

                // Kiểm tra xem vị trí thả có nằm trong grid container không
                var isInsideGrid = dropX >= 0 && dropX <= root.parent.width &&
                                  dropY >= 0 && dropY <= root.parent.height

                // Nếu thả ra ngoài grid container hoặc vị trí không hợp lệ, trở về vị trí cũ
                if (!isInsideGrid || targetPos < 0 || (gridModel && targetPos >= gridModel.columns * gridModel.rows)) {
                    isSwapping = false
                    returnAnimation.start()
                    return
                }

                // Nếu thả vào vị trí hợp lệ và trong phạm vi grid
                if (targetPos >= 0 && gridModel && targetPos < gridModel.columns * gridModel.rows) {
                    // Check if dropping on itself - if so, restore position
                    if (targetPos === position) {
                        root.x = adjustedGridCol * (root.parent.width / gridModel.columns)
                        root.y = adjustedGridRow * (root.parent.height / gridModel.rows)
                        isSwapping = false
                        return
                    }

                    if (!dropItem) {
                        // Nếu thả vào ô trống
                        var oldPos = position

                        // Đã lấy kích thước hiện tại của item ở trên
                        // widthCells và heightCells đã được tính toán

                        // Kiểm tra xem có đủ ô trống liên tiếp không
                        var hasEnoughSpace = true
                        for (var i = 0; i < heightCells; i++) {
                            for (var j = 0; j < widthCells; j++) {
                                var checkPos = targetPos + i * gridModel.columns + j
                                // Kiểm tra vị trí có nằm trong grid không
                                if (checkPos >= gridModel.columns * gridModel.rows ||
                                    Math.floor(checkPos / gridModel.columns) !== Math.floor((targetPos + i * gridModel.columns) / gridModel.columns)) {
                                    hasEnoughSpace = false
                                    break
                                }
                                // Kiểm tra ô có bị chiếm không
                                for (var pos in root.parent.activeItems) {
                                    if (pos === oldPos.toString()) continue
                                    var item = root.parent.activeItems[pos]
                                    if (!item) continue

                                    var itemDimensions = gridModel ? gridModel.getCellDimensions(parseInt(pos)) : {"width": 1, "height": 1}
                                    var itemStartCol = gridModel ? Math.floor(parseInt(pos) % gridModel.columns) : 0
                                    var itemStartRow = gridModel ? Math.floor(parseInt(pos) / gridModel.columns) : 0
                                    var itemEndCol = itemStartCol + itemDimensions.width - 1
                                    var itemEndRow = itemStartRow + itemDimensions.height - 1

                                    var checkCol = gridModel ? Math.floor(checkPos % gridModel.columns) : 0
                                    var checkRow = gridModel ? Math.floor(checkPos / gridModel.columns) : 0

                                    if (checkCol >= itemStartCol && checkCol <= itemEndCol &&
                                        checkRow >= itemStartRow && checkRow <= itemEndRow) {
                                        hasEnoughSpace = false
                                        break
                                    }
                                }
                                if (!hasEnoughSpace) break
                            }
                            if (!hasEnoughSpace) break
                        }

                        if (!hasEnoughSpace) {
                            isSwapping = false
                            returnAnimation.start()
                            return
                        }

                        // Cập nhật position cho GridItem
                        position = targetPos

                        // Cập nhật vị trí hiển thị, giữ nguyên kích thước
                        if (gridModel) {
                            root.width = (root.parent.width / gridModel.columns) * widthCells
                            root.height = (root.parent.height / gridModel.rows) * heightCells
                            root.x = adjustedGridCol * (root.parent.width / gridModel.columns)
                            root.y = adjustedGridRow * (root.parent.height / gridModel.rows)
                        }

                        // Cập nhật trong activeItems - chỉ cập nhật khi vị trí thực sự thay đổi
                        if (oldPos !== targetPos) {
                            // Lưu trữ item hiện tại
                            var currentItem = root.parent.activeItems[oldPos]
                            // Xóa khỏi vị trí cũ
                            delete root.parent.activeItems[oldPos]
                            // Thêm vào vị trí mới
                            root.parent.activeItems[targetPos] = currentItem
                            // Đặt isSave = false trước khi cập nhật vị trí
                            if (gridModel) {
                                gridModel.isSave = false
                            }

                            // Thông báo cho GridModel
                            gridModel.updateVideoPosition(oldPos, targetPos)
                            // Đảm bảo kích thước được giữ nguyên khi di chuyển
                            // var currentDimensions = gridManager.getCellDimensions(targetPos)
                            // gridManager.updateCellDimensions(targetPos, widthCells, heightCells)
                        }

                        isSwapping = false
                        return
                    } else if (dropItem.position !== position) {
                        // Nếu thả vào ô đã có video -> thực hiện swap
                        var sourcePos = position
                        var targetPos = dropItem.position

                        // Get dimensions for both items
                        var sourceDimensions = gridModel ? gridModel.getCellDimensions(sourcePos) : {"width": 1, "height": 1}
                        var targetDimensions = gridModel ? gridModel.getCellDimensions(targetPos) : {"width": 1, "height": 1}

                        // Cập nhật position cho cả hai GridItem
                        position = targetPos
                        dropItem.position = sourcePos

                        // Cập nhật kích thước và vị trí hiển thị cho cả hai GridItem
                        if (gridModel) {
                            root.width = (root.parent.width / gridModel.columns) * targetDimensions.width
                            root.height = (root.parent.height / gridModel.rows) * targetDimensions.height
                            root.x = (targetPos % gridModel.columns) * (root.parent.width / gridModel.columns)
                            root.y = Math.floor(targetPos / gridModel.columns) * (root.parent.height / gridModel.rows)

                            dropItem.width = (root.parent.width / gridModel.columns) * sourceDimensions.width
                            dropItem.height = (root.parent.height / gridModel.rows) * sourceDimensions.height
                            dropItem.x = (sourcePos % gridModel.columns) * (root.parent.width / gridModel.columns)
                            dropItem.y = Math.floor(sourcePos / gridModel.columns) * (root.parent.height / gridModel.rows)
                        }

                        // Cập nhật trong activeItems
                        var tempItem = root.parent.activeItems[sourcePos]
                        root.parent.activeItems[sourcePos] = root.parent.activeItems[targetPos]
                        root.parent.activeItems[targetPos] = tempItem

                                // Thông báo cho GridModel
                        if (gridModel) {
                            // Đặt isSave = false trước khi swap vị trí
                            gridModel.isSave = false
                            // Emit signal to grid manager instead of direct handling
                            gridModel.swapItemsRequested(sourcePos, targetPos)
                        }


                        // Reset flags and z-index after the swap is complete
                        dragActive = false
                        isSwapping = false
                        // Now reset z-index to default
                        root.z = calculateZ()
                        console.log("Swap completed, z-index reset to:", root.z)
                        return
                    }
                }

                // Nếu không thả được vào đâu, trở về vị trí cũ
                // Không reset isSwapping và dragActive ở đây
                // Chúng sẽ được reset khi returnAnimation kết thúc
                returnAnimation.start()
            }

            // Kiểm tra xem camera có nằm trong hàng và cột hợp lệ không
            // Chỉ kiểm tra khi không ở chế độ maximize và không đang trong quá trình maximize/restore
            if (gridModel && !isSwapping && root.camera_id !== "" &&
                !gridModel.isMaximized && !isMaximizing && !isResizing) {
                var finalGridCol = Math.floor(root.x / (root.parent.width / gridModel.columns))
                var finalGridRow = Math.floor(root.y / (root.parent.height / gridModel.rows))
                var finalCellWidth = root.parent.width / gridModel.columns
                var finalCellHeight = root.parent.height / gridModel.rows

                // Kiểm tra xem camera có nằm đúng vị trí của ô trong grid không
                var isAlignedWithGrid = Math.abs(root.x - finalGridCol * finalCellWidth) < 5 &&
                                       Math.abs(root.y - finalGridRow * finalCellHeight) < 5

                if (!isAlignedWithGrid ||
                    finalGridCol < 0 || finalGridCol >= gridModel.columns ||
                    finalGridRow < 0 || finalGridRow >= gridModel.rows) {
                    console.log("Camera is not aligned with grid or outside valid row/column. Col:", finalGridCol,
                               "Row:", finalGridRow, "isAligned:", isAlignedWithGrid)
                    returnAnimation.start()
                }
            }

            } catch (e) {
                console.error("Error in onReleased:", e)
                // Đảm bảo reset các trạng thái quan trọng
                isDragging = false
                dragActive = false
                isSwapping = false
                root.opacity = 1

                // Đặt lại z-index về giá trị bình thường
                root.z = calculateZ()
                console.log("Error handling: z-index reset to:", root.z)

                // Trở về vị trí ban đầu
                returnAnimation.start()
            } finally {
                // Đảm bảo opacity được reset
                isDragging = false
                root.opacity = 1

                // Chỉ reset các trạng thái và z-index nếu không đang trong animation
                if (!returnAnimation.running && !dropAnimation.running) {
                    // Đảm bảo z-index được đặt lại trong mọi trường hợp
                    if (!isMaximizing && !isResizing) {
                        dragActive = false
                        isSwapping = false
                        root.z = calculateZ()
                        console.log("Finally block: z-index reset to:", root.z)
                    }
                }
            }
        }

        onEntered: function(mouse) {
            if (root.camera_id !== "") {
                // // console.log("Mouse entered position:", position);
                root.isHovered = true
            }
        }

        onExited: function(mouse) {
            // // console.log("Mouse exited position:", position);
            checkHoverTimer.start()
        }
    }

    // Chỉ giữ lại returnAnimation để trở về vị trí
    ParallelAnimation {
        id: returnAnimation

        NumberAnimation {
            target: root
            property: "x"
            to: calculateCorrectX()
            duration: 300
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "y"
            to: calculateCorrectY()
            duration: 300
            easing.type: Easing.OutQuad
        }

        onStarted: {
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Return animation started, z-index set to:", root.z)
        }

        onFinished: {
            // Đặt lại z-index về giá trị bình thường khi kết thúc
            dragActive = false
            isSwapping = false
            root.z = calculateZ()
            console.log("Return animation completed, z-index reset to:", root.z)
        }
    }

    // Function to calculate correct X position based on current layout
    function calculateCorrectX() {
        if (!gridModel || !parent) return startX
        var col = position % gridModel.columns
        return (col * parent.width / gridModel.columns)
    }

    // Function to calculate correct Y position based on current layout
    function calculateCorrectY() {
        if (!gridModel || !parent) return startY
        var row = Math.floor(position / gridModel.columns)
        return (row * parent.height / gridModel.rows)
    }

    // Thêm animation fit vào ô target
    ParallelAnimation {
        id: dropAnimation

        NumberAnimation {
            target: root
            property: "x"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "y"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "width"
            duration: 150
            easing.type: Easing.OutQuad
        }

        NumberAnimation {
            target: root
            property: "height"
            duration: 150
            easing.type: Easing.OutQuad
        }

        onStarted: {
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Drop animation started, z-index set to:", root.z)
        }

        onFinished: {
            // Sau khi animation hoàn tất, trở về vị trí ban đầu
            // Không reset z-index ở đây vì returnAnimation sẽ tiếp tục giữ z-index cao
            // z-index sẽ được reset khi returnAnimation kết thúc
            returnAnimation.start()
        }
    }

    // Drop area
    DropArea {
        id: itemDropArea
        anchors.fill: parent
        keys: ["videoItem"]
        enabled: !(gridModel && gridModel.isMaximized) // Không cho phép drop khi ở chế độ fullscreen

        // Thêm thuộc tính để kiểm tra phần trăm chồng lấp
        property real overlapThreshold: 0.5 // 50% overlap required

        onEntered: function(drag) {
            if (drag.source && drag.source !== root) {
                // Kiểm tra phần trăm chồng lấp
                var sourceItem = drag.source
                var targetItem = root

                // Tính toán vùng chồng lấp
                var sourceRect = Qt.rect(sourceItem.x, sourceItem.y, sourceItem.width, sourceItem.height)
                var targetRect = Qt.rect(targetItem.x, targetItem.y, targetItem.width, targetItem.height)

                var overlapRect = calculateOverlapRect(sourceRect, targetRect)
                var overlapArea = overlapRect.width * overlapRect.height
                var sourceArea = sourceItem.width * sourceItem.height

                var overlapPercentage = overlapArea / sourceArea

                // Chỉ hiển thị viền khi đủ phần trăm chồng lấp
                if (overlapPercentage >= overlapThreshold) {
                    root.border.color = dragBorderColor
                }
            }
        }

        onExited: {
            if (isHovered && camera_id !== "") {
                root.border.color = hoverBorderColor
            } else {
                root.border.color = defaultBorderColor
            }
        }

        onDropped: function(drop) {
            // Empty handler
        }

        // Hàm tính toán vùng chồng lấp giữa hai hình chữ nhật
        function calculateOverlapRect(rect1, rect2) {
            var left = Math.max(rect1.x, rect2.x)
            var top = Math.max(rect1.y, rect2.y)
            var right = Math.min(rect1.x + rect1.width, rect2.x + rect2.width)
            var bottom = Math.min(rect1.y + rect1.height, rect2.y + rect2.height)

            var width = Math.max(0, right - left)
            var height = Math.max(0, bottom - top)

            return Qt.rect(left, top, width, height)
        }
    }

    // Timer để kiểm tra lại hover
    Timer {
        id: checkHoverTimer
        interval: 100
        repeat: false
        onTriggered: {
            if (!dragArea.containsMouse &&
                !joystickArea.containsMouse &&
                !closeArea.containsMouse &&
                !maximizeArea.containsMouse &&
                !dragZoomArea.containsMouse &&
                !ptzArea.containsMouse &&
                !ptz3dArea.containsMouse &&
                !ptzControlPanel.visible &&
                !dragZoomControl.visible) {
                root.isHovered = false
            }
        }
    }

    // Listen for video info changes
    Connections {
        target: gridModel
        function onVideoInfoChanged(pos, camera_model, isPlaying, supportsPTZ) {
            if (pos === position) {
                root.camera_id = camera_model ? camera_model.id : ""
                root.isPlaying = isPlaying
                root.supportsPTZ = supportsPTZ
                console.log("Camera at position", pos, "supports PTZ:", supportsPTZ)
            }
        }
    }

    // Add selection highlight
    Rectangle {
        anchors.fill: parent
        color: "#3B82F640"
        visible: isSelected && !itemDropArea.containsDrag
        z: 1
    }

    // Add selection overlay
    Rectangle {
        anchors.fill: parent
        color: isSelected ? (isDarkTheme ? "#3B82F680" : "#60A5FA80") : "transparent"
        visible: isSelected
    }

    // Control buttons container
    Flow {
        id: controlButtonsRow
        objectName: "controlButtonsRow" // Thêm objectName để có thể tìm kiếm từ PTZ3DControl
        anchors {
            top: parent.top
            right: parent.right
            margins: Math.max(1, Math.min(6, root.width / 60)) // Responsive margins
        }
        width: Math.min(root.width * 0.95, 400) // Limit width to allow wrapping
        layoutDirection: Qt.RightToLeft // Left to right order
        spacing: Math.max(1, Math.min(4, root.width / 100)) // Responsive spacing
        visible: root.camera_id !== "" && root.isHovered
        z: 2

        // Calculate button size based on grid item size, with larger minimum
        property real buttonSize: Math.max(22, Math.min(32, root.width / 16))
        property real iconSize: buttonSize * 0.6

        // Close button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: closeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
            border.color: "#FFFFFF"
            border.width: closeArea.containsMouse ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_close.svg" : "qrc:src/assets/camera_stream/icon_close_light.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: closeArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // Tắt các panel PTZ khi nhấn nút này
                    root.activatePtzButton("none");

                    // console.log("[DEBUG_REMOVE_STREAM] Close button clicked for position: " + position);

                    // If in maximized mode and this is the maximized item, restore grid first
                    if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
                        // console.log("[DEBUG_REMOVE_STREAM] Restoring grid before removing video");
                        gridModel.restoreGrid()
                    }

                    // Then remove the video
                    // console.log("[DEBUG_REMOVE_STREAM] Calling removeVideo for position: " + position);
                    if (gridModel) {
                        // Đặt isSave = false trước khi xóa camera
                        gridModel.isSave = false
                        // Emit signal to grid manager instead of direct handling
                        gridModel.removeItemsRequested([position])
                    }
                }
            }
        }

        // Joystick button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: joystickArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
            border.color: "#FFFFFF"
            border.width: joystickArea.containsMouse ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/map/rotate_map.png" : "qrc:src/assets/images/rotate_camera_light.png"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: joystickArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // Tắt các panel PTZ khi nhấn nút này
                    root.activatePtzButton("none");

                    // TODO: Show joystick control panel
                    videoFrame.rotation = (videoFrame.rotation + 90) % 360
                }
            }
        }

        // Maximize button
        Rectangle {
            id: maximizeButton
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            color: maximizeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
            border.color: "#FFFFFF"
            border.width: maximizeArea.containsMouse ? 1 : 0
            // Make button more accessible - show when camera exists and either hovered OR already maximized
            visible: root.camera_id !== "" && gridModel && (root.isHovered || gridModel.isMaximized)

            // Add visual feedback when clicked
            property bool isPressed: false

            Behavior on color {
                ColorAnimation { duration: 100 }
            }

            Image {
                anchors.centerIn: parent
                source: {
                    if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
                        return isDarkTheme ? "qrc:src/assets/camera_stream/shrink_camera.svg" : "qrc:src/assets/camera_stream/shrink_camera_light.svg"
                    } else {
                        return isDarkTheme ? "qrc:src/assets/camera_stream/expand_camera.svg" : "qrc:src/assets/camera_stream/expand_camera_light.svg"
                    }
                }
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: maximizeArea
                anchors.fill: parent
                hoverEnabled: true
                onPressed: {
                    maximizeButton.isPressed = true
                    maximizeButton.color = "rgba(255, 255, 255, 0.3)" // White flash when pressed
                }
                onReleased: {
                    maximizeButton.isPressed = false
                    maximizeButton.color = maximizeArea.containsMouse ? "rgba(0, 0, 0, 0.2)" : "rgba(0, 0, 0, 0.7)"
                }
                onClicked: {
                    // Tắt các panel PTZ khi nhấn nút này
                    root.activatePtzButton("none");

                    console.log("🔍 [ZOOM BUTTON] Maximize button clicked!");
                    console.log("🔍 [ZOOM BUTTON] Position:", position);
                    console.log("🔍 [ZOOM BUTTON] Camera ID:", root.camera_id);
                    console.log("🔍 [ZOOM BUTTON] Is Maximized:", gridModel ? gridModel.isMaximized : "no gridModel");
                    console.log("🔍 [ZOOM BUTTON] Active Item Position:", gridModel ? gridModel.activeItemPosition : "no gridModel");
                    console.log("🔍 [ZOOM BUTTON] Is Maximizing:", isMaximizing);
                    console.log("🔍 [ZOOM BUTTON] Is Resizing:", isResizing);

                    // Check if we can proceed
                    if (isMaximizing || isResizing) {
                        console.log("❌ [ZOOM BUTTON] Cannot proceed - animation in progress");
                        return;
                    }

                    if (!gridModel) {
                        console.log("❌ [ZOOM BUTTON] Cannot proceed - no gridModel");
                        return;
                    }

                    if (root.camera_id === "") {
                        console.log("❌ [ZOOM BUTTON] Cannot proceed - no camera");
                        return;
                    }

                    // Simple toggle logic - if maximized, restore; if not maximized, maximize
                    if (gridModel.isMaximized) {
                        if (position === gridModel.activeItemPosition) {
                            console.log("✅ [ZOOM BUTTON] Restoring from fullscreen...");
                            // Đặt isSave = false trước khi restore grid
                            gridModel.isSave = false;
                            // Emit signal to grid manager instead of direct handling
                            gridModel.toggleFullscreenRequested(position);
                        } else {
                            console.log("❌ [ZOOM BUTTON] Cannot restore - not the active item");
                        }
                    } else {
                        console.log("✅ [ZOOM BUTTON] Maximizing to fullscreen...");
                        // Đặt isSave = false trước khi maximize grid
                        gridModel.isSave = false;
                        isMaximizing = true; // Set z-index cao nhất trước
                        maximizeAnimation.start();
                        // Emit signal to grid manager instead of direct handling
                        gridModel.toggleFullscreenRequested(position);
                    }
                }
            }
        }
        // Drag to zoom PTZ button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            visible: root.supportsPTZ // Only show for cameras that support PTZ
            color: {
                if (root.isDragZoomActive) {
                    return "#303750" // Màu xanh khi đang active
                } else if (dragZoomArea.containsMouse) {
                    return "rgba(0, 0, 0, 0.2)" // Màu khi hover
                } else {
                    return "rgba(0, 0, 0, 0.7)" // Màu mặc định
                }
            }
            border.color: "#FFFFFF"
            border.width: dragZoomArea.containsMouse || root.isDragZoomActive ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_drag_zoom_off.svg" : "qrc:src/assets/camera_stream/icon_drag_zoom_off_light.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: dragZoomArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: function(mouse) {
                    // Chọn camera này khi nhấn vào nút Drag Zoom
                    selectThisCamera();

                    // Toggle Drag Zoom control
                    if (root.isDragZoomActive) {
                        // Nếu đang active thì tắt đi
                        root.activatePtzButton("none");
                    } else {
                        // Nếu chưa active thì bật lên
                        root.activatePtzButton("dragZoom");
                    }
                    console.log("Drag Zoom button clicked, active:", root.isDragZoomActive);

                    // Ngăn sự kiện truyền qua
                    mouse.accepted = true;
                }
            }
        }

        // PTZ button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            visible: root.supportsPTZ // Only show for cameras that support PTZ
            color: {
                if (root.isPtzActive) {
                    return "#303750" // Màu xanh khi đang active
                } else if (ptzArea.containsMouse) {
                    return "rgba(0, 0, 0, 0.2)" // Màu khi hover
                } else {
                    return "rgba(0, 0, 0, 0.7)" // Màu mặc định
                }
            }
            border.color: "#FFFFFF"
            border.width: ptzArea.containsMouse || root.isPtzActive ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_ptz_off.svg" : "qrc:src/assets/camera_stream/icon_ptz_off_light.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: ptzArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // Chọn camera này khi nhấn vào nút PTZ
                    selectThisCamera();

                    // Toggle PTZ panel
                    if (root.isPtzActive) {
                        // Nếu đang active thì tắt đi
                        root.activatePtzButton("none");
                    } else {
                        // Nếu chưa active thì bật lên
                        root.activatePtzButton("ptz");
                    }
                    console.log("PTZ button clicked, active:", root.isPtzActive);
                }
            }
        }

        // 3D PTZ button
        Rectangle {
            width: controlButtonsRow.buttonSize
            height: controlButtonsRow.buttonSize
            radius: width / 7
            visible: root.supportsPTZ // Only show for cameras that support PTZ
            color: {
                if (root.isPtz3dActive) {
                    return "#303750" // Màu xanh khi đang active
                } else if (ptz3dArea.containsMouse) {
                    return "rgba(0, 0, 0, 0.2)" // Màu khi hover
                } else {
                    return "rgba(0, 0, 0, 0.7)" // Màu mặc định
                }
            }
            border.color: "#FFFFFF"
            border.width: ptz3dArea.containsMouse || root.isPtz3dActive ? 1 : 0

            Image {
                anchors.centerIn: parent
                source: isDarkTheme ? "qrc:src/assets/camera_stream/icon_ptz_arrow_off.svg" : "qrc:src/assets/camera_stream/icon_ptz_arrow_off_light.svg"
                width: controlButtonsRow.iconSize
                height: controlButtonsRow.iconSize
                fillMode: Image.PreserveAspectFit
            }

            MouseArea {
                id: ptz3dArea
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    // Chọn camera này khi nhấn vào nút PTZ 3D Area
                    selectThisCamera();

                    // Toggle PTZ 3D control
                    if (root.isPtz3dActive) {
                        // Nếu đang active thì tắt đi
                        root.activatePtzButton("none");
                    } else {
                        // Nếu chưa active thì bật lên
                        root.activatePtzButton("ptz3d");
                    }
                    console.log("3D PTZ button clicked, active:", root.isPtz3dActive);
                }
            }
        }

    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 8
        spacing: 4

        // Video display area
        Rectangle {
            id: videoArea
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "transparent"

            // Placeholder when no video
            Rectangle {
                anchors.fill: parent
                color: "transparent"
                radius: 4
                visible: !root.camera_id || !root.isPlaying
            }
            // Zoom Mouse Area - handles both PTZ zoom and digital zoom
            MouseArea {
                id: zoomMouseArea
                anchors.fill: parent
                hoverEnabled: true
                acceptedButtons: Qt.NoButton // Chỉ xử lý wheel, không xử lý click
                propagateComposedEvents: true // Cho phép sự kiện truyền xuống các MouseArea khác
                z: 10 // Đảm bảo MouseArea này nằm trên tất cả các MouseArea khác

                // Hoạt động khi camera đang ở chế độ maximize hoặc camera hỗ trợ PTZ
                enabled: (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) ||
                         (root.supportsPTZ && root.camera_id !== "")

                onWheel: function(wheel) {
                    // Nếu Ctrl được nhấn, chuyển sự kiện lên parent để xử lý resize grid
                    if (wheel.modifiers & Qt.ControlModifier) {
                        wheel.accepted = false; // Cho phép sự kiện truyền lên parent
                        return;
                    }

                    if (!enabled) {
                        wheel.accepted = false;
                        return;
                    }

                    // Luôn chặn sự kiện wheel khi không có Ctrl
                    wheel.accepted = true;

                    // Tính toán delta chuẩn hóa
                    var delta = wheel.angleDelta.y / 120; // Chuẩn hóa delta

                    // Nếu camera hỗ trợ PTZ, ưu tiên sử dụng PTZ zoom trong mọi trường hợp
                    if (root.supportsPTZ && root.camera_id !== "") {
                        // Xác định hướng zoom hiện tại
                        var newZoomDirection = delta > 0 ? "zoom_in" : "zoom_out";

                        // Tính toán hệ số zoom cho PTZ (sử dụng ptzZoomSpeed từ thuộc tính)
                        var zoomFactor = delta > 0 ? root.ptzZoomSpeed : -root.ptzZoomSpeed;

                        // Hiển thị mức độ zoom
                        zoomIndicator.opacity = 1.0;
                        zoomIndicatorTimer.restart();

                        // Kiểm tra xem hướng zoom có thay đổi không
                        if (newZoomDirection !== root.currentZoomDirection && root.isPTZZooming) {
                            // Nếu hướng thay đổi, dừng zoom hiện tại trước khi bắt đầu zoom mới
                            if (gridModel) {
                                console.log("Direction changed, stopping previous zoom");
                                gridModel.stopPTZ(position);
                            }
                        }

                        // Cập nhật hướng zoom hiện tại
                        root.currentZoomDirection = newZoomDirection;

                        console.log("PTZ Zooming with factor:", zoomFactor);

                        // Gọi hàm điều khiển PTZ zoom từ gridModel
                        if (gridModel) {
                            // Đánh dấu đang trong trạng thái zoom
                            root.isPTZZooming = true;

                            // Gọi controlPTZZoom với hệ số zoom
                            gridModel.controlPTZZoom(position, zoomFactor);

                            // Khởi động lại timer để dừng zoom sau một khoảng thời gian
                            ptzZoomStopTimer.restart();
                        }
                    }
                    // Nếu camera không hỗ trợ PTZ và đang ở chế độ fullscreen, sử dụng digital zoom
                    else if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
                        // Tính toán vị trí zoom dựa trên vị trí chuột
                        var mousePos = Qt.point(wheel.x, wheel.y);
                        root.zoomCenter = mousePos;

                        // Tính toán hệ số zoom mới cho digital zoom
                        var newZoomFactor = root.zoomFactor + delta * root.zoomSensitivity;

                        // Giới hạn hệ số zoom
                        newZoomFactor = Math.max(root.minZoom, Math.min(root.maxZoom, newZoomFactor));

                        // Áp dụng hệ số zoom mới
                        if (newZoomFactor !== root.zoomFactor) {
                            root.zoomFactor = newZoomFactor;
                            root.isZooming = true;

                            // Hiển thị mức độ zoom
                            zoomIndicator.opacity = 1.0;
                            zoomIndicatorTimer.restart();

                            console.log("Digital Zooming to factor:", root.zoomFactor, "at position:", mousePos.x, mousePos.y);
                        }
                    }
                }
            }

            // Timer để dừng PTZ zoom sau một khoảng thời gian
            Timer {
                id: ptzZoomStopTimer
                interval: 300  // Dừng zoom sau 300ms nếu không có thêm sự kiện wheel
                repeat: false
                running: false

                onTriggered: {
                    console.log("Stopping PTZ zoom after delay");
                    if (gridModel && root.isPTZZooming) {
                        gridModel.stopPTZ(position);
                        root.isPTZZooming = false;
                    }
                }
            }

            // Video frame display
            CustomVideo {
                id: videoFrame
                anchors.centerIn: parent
                width: rotation === 90 || rotation === 270 ? parent.height : parent.width
                height: rotation === 90 || rotation === 270 ? parent.width : parent.height
                camera_id: root.camera_id
                position: root.position
                isPlaying: root.isPlaying
                rotation: 0
                z: 1

                // Áp dụng biến đổi khi digital zoom
                transform: [
                    Scale {
                        id: videoScale
                        origin.x: root.zoomCenter.x
                        origin.y: root.zoomCenter.y
                        xScale: root.zoomFactor
                        yScale: root.zoomFactor
                    }
                ]

                onClicked: function(mouse) {
                    // Chỉ chọn camera khi nhấn Ctrl + chuột trái
                    if (gridModel && !gridModel.isMaximized && (mouse.modifiers & Qt.ControlModifier)) {
                        // Với phím Ctrl, toggle chọn/bỏ chọn
                        root.isSelected = !root.isSelected
                        console.log("Camera selection toggled with Ctrl (from CustomVideo):", root.isSelected)
                    }
                }

                onDoubleClicked: {
                    // Chuyển sự kiện double-click đến dragArea
                    dragArea.doubleClicked(null);
                }

                onRightClicked: {
                    // Hiện menu ngữ cảnh
                    console.log("Right-clicked on GridItem, showing context menu")
                    console.log("Available tabs:", gridModel ? gridModel.availableTabs.length : "No gridModel")

                    // Update context menu based on selection state
                    contextMenu.updateMenuForSelection()
                    contextMenu.popup();
                }
                Component.onCompleted: {
                    console.log("[GridItem.qml] CustomVideo created at position:", root.position, "camera_id:", root.camera_id)
                }
            }

            // Hiển thị mức độ zoom (digital hoặc PTZ)
            Rectangle {
                id: zoomIndicator
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.bottom: parent.bottom
                anchors.bottomMargin: 20
                width: 100
                height: 30
                radius: 15
                color: "transparent"
                opacity: 0
                visible: opacity > 0
                z: 10

                Behavior on opacity {
                    NumberAnimation { duration: 200 }
                }

                property string text: Math.round(root.zoomFactor * 100) + "%"

                Text {
                    anchors.centerIn: parent
                    text: zoomIndicator.text
                    color: "white"
                    font.pixelSize: 14
                    font.bold: true
                    visible: false // Hide the text as requested
                }

                Timer {
                    id: zoomIndicatorTimer
                    interval: 1500
                    onTriggered: {
                        zoomIndicator.opacity = 0;
                    }
                }
            }
        }

        // Context Menu
        Menu {
            id: contextMenu
            modal: true
            dim: false
            closePolicy: Popup.CloseOnEscape | Popup.CloseOnPressOutside
            width: 200

            // Properties to track selection state
            property var selectedPositions: []
            property int selectedCount: 0
            property bool isMultiSelection: false

            // Apply theme styling
            background: Rectangle {
                color: menuBackgroundColor
                border.color: menuBorderColor
                border.width: 1
                radius: 4
            }

            // Function to update menu based on current selection
            function updateMenuForSelection() {
                selectedPositions = []
                selectedCount = 0

                // Collect all selected positions
                if (root.parent && root.parent.activeItems) {
                    for (var pos in root.parent.activeItems) {
                        if (root.parent.activeItems[pos] && root.parent.activeItems[pos].isSelected) {
                            selectedPositions.push(parseInt(pos))
                            selectedCount++
                        }
                    }
                }

                isMultiSelection = selectedCount > 1
                console.log("Context menu updated - Selected count:", selectedCount, "Positions:", JSON.stringify(selectedPositions))

                // Update menu items text based on selection
                if (isMultiSelection) {
                    newScreenMenuItem.text = "Màn hình mới (" + selectedCount + " cameras)"
                } else {
                    newScreenMenuItem.text = "Màn hình mới"
                }
            }

            delegate: MenuItem {
                id: menuItemDelegate
                implicitWidth: 200
                implicitHeight: 32

                background: Rectangle {
                    color: menuItemDelegate.hovered ? root.menuHoverColor : "transparent"
                    radius: 2
                }

                contentItem: Text {
                    text: menuItemDelegate.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    leftPadding: 8
                    rightPadding: 8
                    verticalAlignment: Text.AlignVCenter
                }
            }

            // Main submenu: "Open camera to..." - similar to camera_widget.py structure
            Menu {
                id: openCameraMenu
                title: "Mở camera sang..."
                width: 200
                // Apply theme styling to submenu
                background: Rectangle {
                    color: root.menuBackgroundColor
                    border.color: root.menuBorderColor
                    border.width: 1
                    radius: 4
                }

                delegate: MenuItem {
                    implicitWidth: 200
                    implicitHeight: 32

                    background: Rectangle {
                        color: parent.hovered ? root.menuHoverColor : "transparent"
                        radius: 2
                    }

                    contentItem: Text {
                        text: parent.text
                        color: root.menuTextColor
                        font.pixelSize: 14
                        leftPadding: 8
                        rightPadding: 8
                        verticalAlignment: Text.AlignVCenter
                    }
                }

                MenuItem {
                    id: newScreenMenuItem
                    text: "Màn hình mới"

                    background: Rectangle {
                        color: newScreenMenuItem.hovered ? root.menuHoverColor : "transparent"
                        radius: 2
                    }

                    contentItem: Text {
                        text: newScreenMenuItem.text
                        color: root.menuTextColor
                        font.pixelSize: 14
                        leftPadding: 8
                        rightPadding: 8
                        verticalAlignment: Text.AlignVCenter
                    }

                    onTriggered: {
                        if (gridModel) {
                            if (contextMenu.isMultiSelection) {
                                console.log("Opening multiple cameras in new view for positions:", JSON.stringify(contextMenu.selectedPositions))
                                gridModel.openMultipleCamerasInNewView(contextMenu.selectedPositions)
                            } else {
                                console.log("Opening camera in new view for position:", position)
                                gridModel.openCameraInNewView(position)
                            }
                        }
                    }
                }

                MenuItem {
                    id: newSavedViewMenuItem
                    text: "Màn hình đã lưu mới"

                    background: Rectangle {
                        color: newSavedViewMenuItem.hovered ? root.menuHoverColor : "transparent"
                        radius: 2
                    }

                    contentItem: Text {
                        text: newSavedViewMenuItem.text
                        color: root.menuTextColor
                        font.pixelSize: 14
                        leftPadding: 8
                        rightPadding: 8
                        verticalAlignment: Text.AlignVCenter
                    }

                    onTriggered: {
                        if (gridModel) {
                            console.log("Opening camera in new saved view for position:", position)
                            gridModel.openCameraInNewSavedView(position)
                        }
                    }
                }

                MenuItem {
                    id: newVirtualWindowMenuItem
                    text: "Cửa sổ giả lập mới"

                    background: Rectangle {
                        color: newVirtualWindowMenuItem.hovered ? root.menuHoverColor : "transparent"
                        radius: 2
                    }

                    contentItem: Text {
                        text: newVirtualWindowMenuItem.text
                        color: root.menuTextColor
                        font.pixelSize: 14
                        leftPadding: 8
                        rightPadding: 8
                        verticalAlignment: Text.AlignVCenter
                    }

                    onTriggered: {
                        if (gridModel) {
                            console.log("Creating new virtual window with camera from position:", position)
                            gridModel.openCameraInNewVirtualWindow(position)
                        }
                    }
                }

                MenuSeparator {
                background: Rectangle {
                    color: root.menuSeparatorColor
                    height: 1
                }
            }

                // Saved view submenu with dynamic tabs
                Menu {
                    id: savedViewMenu
                    title: "Màn hình đã lưu"
                    enabled: gridModel && gridModel.availableTabs
                    width: 200
                    // Apply theme styling to submenu
                    background: Rectangle {
                        color: root.menuBackgroundColor
                        border.color: root.menuBorderColor
                        border.width: 1
                        radius: 4
                    }

                    onAboutToShow: {
                        if (gridModel) {
                            gridModel.refreshAvailableTabs()
                            updateSavedViewMenuItems()
                        }
                    }

                    function updateSavedViewMenuItems() {
                        var savedViews = gridModel ? gridModel.availableTabs.filter(function(tab) { return tab.type === "SavedView" }) : []

                        // Clear existing menu items
                        for (var i = savedViewMenu.count - 1; i >= 0; i--) {
                            savedViewMenu.removeItem(savedViewMenu.itemAt(i))
                        }

                        // Add menu items dynamically based on available saved views
                        for (var j = 0; j < savedViews.length; j++) {
                            var savedView = savedViews[j]

                            // Create MenuItem with theme styling using Qt.createQmlObject
                            try {
                                var menuItemQml = `
                                    import QtQuick 2.15
                                    import QtQuick.Controls 2.15
                                    MenuItem {
                                        property var tabData: null

                                        background: Rectangle {
                                            color: parent.hovered ? "${root.menuHoverColor}" : "transparent"
                                            radius: 2
                                        }

                                        contentItem: Text {
                                            text: parent.text
                                            color: "${root.menuTextColor}"
                                            font.pixelSize: 14
                                            leftPadding: 8
                                            rightPadding: 8
                                            verticalAlignment: Text.AlignVCenter
                                        }

                                        onTriggered: {
                                            if (tabData && gridModel) {
                                                gridModel.openCameraInExistingTab(position, tabData.name, 0, 0)
                                            }
                                        }
                                    }`
                                var menuItem = Qt.createQmlObject(menuItemQml, savedViewMenu)
                                menuItem.text = savedView.name
                                menuItem.tabData = savedView
                                savedViewMenu.addItem(menuItem)
                            } catch (e) {
                                console.log("Error creating saved view menu item:", e.toString())
                            }
                        }
                    }
                }

                // Virtual window submenu with dynamic tabs
                Menu {
                    id: virtualWindowMenu
                    title: "Cửa sổ giả lập"
                    enabled: gridModel && gridModel.availableTabs
                    width: 200
                    // Apply theme styling to submenu
                    background: Rectangle {
                        color: root.menuBackgroundColor
                        border.color: root.menuBorderColor
                        border.width: 1
                        radius: 4
                    }

                    onAboutToShow: {
                        if (gridModel) {
                            gridModel.refreshAvailableTabs()
                            updateVirtualWindowMenuItems()
                        }
                    }

                    function updateVirtualWindowMenuItems() {
                        var virtualWindows = gridModel ? gridModel.availableTabs.filter(function(tab) { return tab.type === "VirtualWindow" }) : []

                        // Clear existing menu items
                        for (var i = virtualWindowMenu.count - 1; i >= 0; i--) {
                            virtualWindowMenu.removeItem(virtualWindowMenu.itemAt(i))
                        }

                        // Add menu items dynamically based on available virtual windows
                        for (var j = 0; j < virtualWindows.length; j++) {
                            var virtualWindow = virtualWindows[j]

                            // Create MenuItem with theme styling using Qt.createQmlObject
                            try {
                                var menuItemQml = `
                                    import QtQuick 2.15
                                    import QtQuick.Controls 2.15
                                    MenuItem {
                                        property var tabData: null

                                        background: Rectangle {
                                            color: parent.hovered ? "${root.menuHoverColor}" : "transparent"
                                            radius: 2
                                        }

                                        contentItem: Text {
                                            text: parent.text
                                            color: "${root.menuTextColor}"
                                            font.pixelSize: 14
                                            leftPadding: 8
                                            rightPadding: 8
                                            verticalAlignment: Text.AlignVCenter
                                        }

                                        onTriggered: {
                                            if (tabData && gridModel) {
                                                gridModel.openCameraInExistingTab(position, tabData.name, 0, 0)
                                            }
                                        }
                                    }`
                                var menuItem = Qt.createQmlObject(menuItemQml, virtualWindowMenu)
                                menuItem.text = virtualWindow.name
                                menuItem.tabData = virtualWindow
                                virtualWindowMenu.addItem(menuItem)
                            } catch (e) {
                                console.log("Error creating virtual window menu item:", e.toString())
                            }
                        }
                    }
                }
            }

            MenuSeparator {}

            // Remove from view action - similar to camera_widget.py
            MenuItem {
                id: deleteMenuItem
                text: "Xóa khỏi view\tDel"

                background: Rectangle {
                    color: deleteMenuItem.hovered ? root.menuHoverColor : "transparent"
                    radius: 2
                }

                contentItem: Text {
                    text: deleteMenuItem.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    leftPadding: 8
                    rightPadding: 8
                    verticalAlignment: Text.AlignVCenter
                }

                onTriggered: {
                    if (!gridModel) return;

                    // Thu thập các camera được chọn
                    var selectedItems = [];
                    for (var pos in root.parent.activeItems) {
                        if (root.parent.activeItems[pos] && root.parent.activeItems[pos].isSelected) {
                            selectedItems.push(parseInt(pos));
                        }
                    }

                    console.log("Selected items: " + JSON.stringify(selectedItems));

                    if (selectedItems.length > 0) {
                        console.log("Deleting " + selectedItems.length + " selected cameras");
                        if (selectedItems.length === 1) {
                            // Single camera removal
                            try {
                                if (gridModel) {
                                    gridModel.isSave = false
                                }
                                var result1 = gridModel.removeVideo(selectedItems[0]);
                                console.log("removeVideo result: " + result1);
                            } catch (e) {
                                console.error("Error calling removeVideo: " + e);
                            }
                        } else {
                            // Multiple camera removal
                            try {
                                var qtArray = [];
                                for (var i = 0; i < selectedItems.length; i++) {
                                    qtArray.push(selectedItems[i]);
                                }
                                var result2 = gridModel.removeMultipleVideos(qtArray);
                                console.log("removeMultipleVideos result: " + result2);
                            } catch (e) {
                                console.error("Error calling removeMultipleVideos: " + e);
                            }
                        }
                    } else {
                        console.log("No cameras selected");
                    }
                }
            }

            MenuSeparator {
                background: Rectangle {
                    color: root.menuSeparatorColor
                    height: 1
                }
            }

            // AI Flow submenu - similar to camera_widget.py
            Menu {
                title: "Bài toán AI"
                enabled: gridModel && gridModel.aiSolutions
                width: 200
                // Apply theme styling to submenu
                background: Rectangle {
                    color: root.menuBackgroundColor
                    border.color: root.menuBorderColor
                    border.width: 1
                    radius: 4
                }

                MenuItem {
                    text: "Nhận diện"
                    checkable: true
                    onTriggered: {
                        if (gridModel) gridModel.toggleAiFlow(position, "Recognition")
                    }
                }
                MenuItem {
                    text: "Bảo vệ"
                    checkable: true
                    onTriggered: {
                        if (gridModel) gridModel.toggleAiFlow(position, "Protection")
                    }
                }
                MenuItem {
                    text: "Tần suất"
                    checkable: true
                    onTriggered: {
                        if (gridModel) gridModel.toggleAiFlow(position, "Frequency")
                    }
                }
            }

            // Video streams submenu - similar to camera_widget.py
            Menu {
                title: "Luồng video"
                width: 200
                // Apply theme styling to submenu
                background: Rectangle {
                    color: root.menuBackgroundColor
                    border.color: root.menuBorderColor
                    border.width: 1
                    radius: 4
                }
                MenuItem {
                    text: "Luồng chính"
                    checkable: true
                    onTriggered: gridModel ? gridModel.switchToMainStream(position) : {}
                }
                MenuItem {
                    text: "Luồng phụ"
                    checkable: true
                    onTriggered: gridModel ? gridModel.switchToSubStream(position) : {}
                }
                MenuItem {
                    text: "Luồng AI"
                    checkable: true
                    onTriggered: gridModel ? gridModel.switchToAiStream(position) : {}
                }
            }

            MenuSeparator {}

            // Settings action - similar to camera_widget.py
            MenuItem {
                id: settingsMenuItem
                text: "Cài đặt\tI"

                background: Rectangle {
                    color: settingsMenuItem.hovered ? root.menuHoverColor : "transparent"
                    radius: 2
                }

                contentItem: Text {
                    text: settingsMenuItem.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    leftPadding: 8
                    rightPadding: 8
                    verticalAlignment: Text.AlignVCenter
                }

                onTriggered: {
                    if (gridModel) gridModel.showCameraInfo(position)
                }
            }

            MenuSeparator {}

            // Fullscreen action - similar to camera_widget.py
            MenuItem {
                id: fullscreenMenuItem
                text: gridModel && gridModel.isMaximized && gridModel.activeItemPosition === position ?
                      "Thoát toàn màn hình\tEsc" : "Toàn màn hình\tEnter"
                enabled: gridModel && (gridModel.getActivePositions().length > 1 || gridModel.isMaximized)

                background: Rectangle {
                    color: fullscreenMenuItem.hovered ? root.menuHoverColor : "transparent"
                    radius: 2
                }

                contentItem: Text {
                    text: fullscreenMenuItem.text
                    color: root.menuTextColor
                    font.pixelSize: 14
                    leftPadding: 8
                    rightPadding: 8
                    verticalAlignment: Text.AlignVCenter
                }

                onTriggered: {
                    if (gridModel) {
                        if (gridModel.isMaximized && gridModel.activeItemPosition === position) {
                            gridModel.restoreGrid()
                        } else {
                            gridModel.maximizeGrid(position)
                        }
                    }
                }
            }
        }
    }

    // animation
    scale: 0.5  // Bắt đầu từ kích thước nhỏ
    opacity: 0  // Ẩn ban đầu
    Behavior on scale { NumberAnimation { duration: 300; easing.type: Easing.OutBack } }

    Component.onCompleted: {
        scale = 1
        opacity = 1
    }
    Connections {
        target: gridModel
        function onLayoutChanged(activePositions) {
            // Sử dụng activePositions được truyền từ signal thay vì gọi getActivePositions
            // Không cần làm gì ở đây vì StreamingScreen1.qml đã xử lý layoutChanged
        }
        function onIsMaximizedChanged() {
            // Handle maximize/restore state changes
            if (gridModel.isMaximized && position === gridModel.activeItemPosition) {
                // This item is being maximized
                isMaximizing = true;
                root.z = 5000;
                maximizeAnimation.start();
            } else if (!gridModel.isMaximized && position === gridModel.activeItemPosition) {
                // This item is being restored
                isResizing = true;
                root.z = 5000;
                restoreAnimation.start();
                // Reset digital zoom khi restore
                resetZoom();
            }
        }
        function onActiveItemPositionChanged() {
            // Handle active item position changes
            if (gridModel.isMaximized) {
                if (position === gridModel.activeItemPosition) {
                    // This item became the active maximized item
                    root.z = 5000;
                } else {
                    // This item is no longer the active maximized item
                    root.z = calculateZ();
                }
            }
        }
    }
    // Add maximize/restore animations
    NumberAnimation {
        id: maximizeAnimation
        target: root
        property: "scale"
        from: 1.0
        to: 1.0
        duration: 300
        easing.type: Easing.OutQuad
        onStarted: {
            isMaximizing = true
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Maximize animation started, z-index set to:", root.z)
        }
        onFinished: {
            isMaximizing = false
            // Đặt lại z-index về giá trị phù hợp cho chế độ fullscreen
            root.z = calculateZ()
            console.log("Maximize animation completed, z-index reset to:", root.z)
        }
    }


    NumberAnimation {
        id: restoreAnimation
        target: root
        property: "scale"
        from: 1.0
        to: 1.0
        duration: 300
        easing.type: Easing.OutQuad
        onStarted: {
            isResizing = true
            // Đặt z-index cao khi bắt đầu animation
            root.z = 5000
            console.log("Restore animation started, z-index set to:", root.z)
            // Reset digital zoom khi restore
            resetZoom()
        }
        onFinished: {
            isResizing = false
            // Đặt lại z-index về giá trị thấp hơn khi kết thúc
            root.z = calculateZ()
            console.log("Restore animation completed, z-index reset to:", root.z)
        }
    }

    // Hàm reset digital zoom
    function resetZoom() {
        zoomFactor = 1.0
        zoomCenter = Qt.point(videoFrame.width / 2, videoFrame.height / 2)
        isZooming = false
    }

    // QML canResizeToSize function removed - now using Python gridModel.canResizeToSize() method

    // Hàm kiểm tra có camera trong ô bên phải - giữ lại để tương thích
    function isCameraInRight() {
        if (!gridModel) return false;
        var currentDimensions = gridModel.getCellDimensions(position);
        var widthCells = currentDimensions.width;

        return !gridModel.canResizeToSize(position, widthCells + 1, currentDimensions.height);
    }

    // Hàm kiểm tra có camera trong ô bên dưới - giữ lại để tương thích
    function isCameraInBottom() {
        if (!gridModel) return false;
        var currentDimensions = gridModel.getCellDimensions(position);
        var heightCells = currentDimensions.height;

        return !gridModel.canResizeToSize(position, currentDimensions.width, heightCells + 1);
    }

    // Hàm kiểm tra có camera trong ô bên trái - giữ lại để tương thích
    function isCameraInLeft() {
        if (!gridModel) return false;
        var leftPos = position - 1; // Giả sử ô bên trái là position - 1
        return leftPos >= 0 &&
               root.parent && root.parent.activeItems && root.parent.activeItems[leftPos] !== undefined;
    }

    // Hàm kiểm tra có camera trong ô bên trên - giữ lại để tương thích
    function isCameraInTop() {
        if (!gridModel) return false;
        var topPos = position - gridModel.columns; // Giả sử ô bên trên là position - số cột
        return topPos >= 0 &&
               root.parent && root.parent.activeItems && root.parent.activeItems[topPos] !== undefined;
    }

    // Timer để đặt lại dragActive nếu onReleased không được gọi

    // PTZ Control Panel
    PTZControlPanel {
        id: ptzControlPanel
        parent: root.parent // Đặt parent là CameraGrid thay vì GridItem

        // Đặt vị trí bên phải camera và cách trên một khoảng bằng với hàng nút điều khiển
        x: root.x + root.width + 10
        y: root.y + controlButtonsRow.y + controlButtonsRow.height + 10

        width: 150 // Kích thước cố định
        height: 180 // Kích thước cố định
        isDarkTheme: root.isDarkTheme
        buttonSize: 30 // Kích thước cố định cho các nút
        iconSize: 15 // Kích thước cố định cho các biểu tượng
        z: 9999 // Đảm bảo hiển thị trên tất cả các thành phần khác
        visible: false

        // Xử lý tín hiệu di chuyển PTZ
        onPtzMove: function(direction) {
            console.log("PTZ Move:", direction);
            // Gọi hàm điều khiển PTZ từ gridModel
            if (gridModel) {
                if (direction === "stop") {
                    gridModel.stopPTZ(position);
                } else if (direction === "up") {
                    gridModel.controlPTZ(position, 0, 0, 0, 1);
                } else if (direction === "down") {
                    gridModel.controlPTZ(position, 0, 0, 0, -1);
                } else if (direction === "left") {
                    gridModel.controlPTZ(position, 0, 0, -1, 0);
                } else if (direction === "right") {
                    gridModel.controlPTZ(position, 0, 0, 1, 0);
                } else if (direction === "top-left") {
                    gridModel.controlPTZ(position, 0, 0, -1, 1);
                } else if (direction === "top-right") {
                    gridModel.controlPTZ(position, 0, 0, 1, 1);
                } else if (direction === "bottom-left") {
                    gridModel.controlPTZ(position, 0, 0, -1, -1);
                } else if (direction === "bottom-right") {
                    gridModel.controlPTZ(position, 0, 0, 1, -1);
                } else if (direction === "home") {
                    gridModel.controlPTZ(position, 0, 0, 1, 0);
                }
            }
        }

        // Xử lý tín hiệu zoom
        onPtzZoom: function(factor) {
            console.log("PTZ Zoom:", factor);

            // Gọi hàm điều khiển zoom từ gridModel và dừng ngay lập tức
            if (gridModel) {
                gridModel.controlPTZZoom(position, factor);
                // Dừng zoom bằng cách gọi stopPTZ thay vì stopPTZZoom
                gridModel.stopPTZ(position);
            }
        }

        // Xử lý tín hiệu tốc độ
        onPtzSpeed: function(speed) {
            console.log("PTZ Speed:", speed);

            // Gọi hàm điều khiển tốc độ từ gridModel
            if (gridModel) {
                gridModel.setPTZSpeed(position, speed);
            }
        }

        // Xử lý khi PTZ UI bị đóng
        onPtzClose: {
            console.log("PTZ Panel closed");
            root.isPtzActive = false;

            // Dừng điều khiển PTZ
            if (gridModel) {
                gridModel.stopPTZ(position);
            }
        }

        // Xử lý khi nhả nút PTZ
        onPtzStop: {
            console.log("PTZ Button released - stopping PTZ");

            // Dừng điều khiển PTZ
            if (gridModel) {
                gridModel.stopPTZ(position);
            }
        }
    }

    // PTZ 3D Control UI
    PTZ3DControl {
        id: ptz3dControl
        anchors.fill: parent
        isDarkTheme: root.isDarkTheme
        z: 10 // Đảm bảo hiển thị trên video nhưng dưới các nút

        // Xử lý tín hiệu di chuyển PTZ
        onPtzMove: function(startPoint, endPoint) {
            // Gửi lệnh điều khiển PTZ đến camera
            console.log("PTZ 3D Move - Start:", startPoint.x, startPoint.y, "End:", endPoint.x, endPoint.y);

            // Gọi hàm điều khiển PTZ từ gridModel nếu cần
            if (gridModel) {
                gridModel.controlPTZ(position, startPoint, endPoint);
            }
        }

        // Xử lý khi PTZ UI bị đóng
        onPtzClosed: {
            console.log("PTZ 3D Control closed");
            root.isPtz3dActive = false;

            // Dừng điều khiển PTZ
            if (gridModel) {
                gridModel.stopPTZ(position);
            }
        }

        // Xử lý khi người dùng dừng di chuyển PTZ (nhả chuột)
        onPtzStop: {
            console.log("PTZ 3D Stop - Position:", position);

            // Dừng điều khiển PTZ
            if (gridModel) {
                console.log("Calling gridManager.stopPTZ with position:", position);
                gridModel.stopPTZ(position);
            } else {
                console.error("gridManager is not available");
            }
        }

        // Xử lý tín hiệu zoom từ lăn chuột
        onPtzZoom: function(factor) {
            console.log("PTZ 3D Zoom:", factor);

            // Gọi hàm điều khiển zoom từ gridManager và dừng ngay lập tức
            if (gridModel) {
                gridModel.controlPTZZoom(position, factor);
                // Dừng zoom bằng cách gọi stopPTZ thay vì stopPTZZoom
                gridModel.stopPTZ(position);
            }
        }
    }

    // Drag Zoom Control UI
    DragZoomControl {
        id: dragZoomControl
        anchors.fill: parent
        isDarkTheme: root.isDarkTheme
        z: 10 // Đảm bảo hiển thị trên video nhưng dưới các nút
        visible: false

        // Xử lý tín hiệu khi vẽ hình chữ nhật để zoom
        onDragZoom: function(startPoint, endPoint) {
            // Gửi lệnh điều khiển drag-to-zoom đến camera
            console.log("Drag Zoom - Start:", startPoint.x, startPoint.y, "End:", endPoint.x, endPoint.y);

            // Gọi hàm điều khiển drag-to-zoom từ gridModel
            if (gridModel) {
                // Gọi hàm set_drag_to_zoom từ gridModel
                // Truyền trực tiếp các giá trị x, y thay vì QPoint
                gridModel.setDragToZoom(position, startPoint.x, startPoint.y, endPoint.x, endPoint.y, width, height);

            }
        }

        // Xử lý khi Drag Zoom UI bị đóng
        onDragZoomClosed: {
            console.log("Drag Zoom Control closed");
            // Tắt chế độ dragZoom khi nhận tín hiệu này
            root.isDragZoomActive = false;
        }
    }

    // Camera State Icon and Name Display (only for cameras)
    Row {
        id: cameraInfoDisplay
        anchors.bottom: parent.bottom
        anchors.left: parent.left
        anchors.margins: 8
        z: 1000
        spacing: 8
        visible: root.camera_id !== "" && itemData // Only show for cameras

        // Camera State Icon
        Image {
            id: cameraStateIcon
            width: 14
            height: 14
            source: getCameraStateIcon()
            fillMode: Image.PreserveAspectFit

            function getCameraStateIcon() {
                if (!itemData || itemData.state_merged === undefined) {
                    console.log("DEBUG: itemData or state_merged is undefined - itemData:", itemData, "state_merged:", itemData ? itemData.state_merged : "N/A")
                    return ""
                }

                console.log("DEBUG: Camera state_merged value:", itemData.state_merged)

                // Using CommonEnum.CameraState values with Style.PrimaryImage icons
                switch(itemData.state_merged) {
                    case CommonEnum.CameraState.CONNECTED_REC_PIN: // 0
                        return "qrc:/src/assets/treeview_and_menu_treeview/connected_rec_pin.svg"
                    case CommonEnum.CameraState.CONNECTED_REC_UNPIN: // 1
                        return "qrc:/src/assets/treeview_and_menu_treeview/connected_rec_unpin.svg"
                    case CommonEnum.CameraState.CONNECTED_NOREC_PIN: // 2
                        return "qrc:/src/assets/treeview_and_menu_treeview/connected_norec_pin.svg"
                    case CommonEnum.CameraState.CONNECTED_NOREC_UNPIN: // 3
                        return "qrc:/src/assets/treeview_and_menu_treeview/connected_norec_unpin.svg"
                    case CommonEnum.CameraState.DISCONNECTED_REC_PIN: // 4
                        return "qrc:/src/assets/treeview_and_menu_treeview/disconnected_rec_pin.svg"
                    case CommonEnum.CameraState.DISCONNECTED_REC_UNPIN: // 5
                        return "qrc:/src/assets/treeview_and_menu_treeview/disconnected_rec_unpin.svg"
                    case CommonEnum.CameraState.DISCONNECTED_NOREC_PIN: // 6
                        return "qrc:/src/assets/treeview_and_menu_treeview/disconnected_norec_pin.svg"
                    case CommonEnum.CameraState.DISCONNECTED_NOREC_UNPIN: // 7
                        return "qrc:/src/assets/treeview_and_menu_treeview/disconnected_norec_unpin.svg"
                    default:
                        console.log("DEBUG: Unknown state_merged value:", itemData.state_merged)
                        return "qrc:/src/assets/treeview_and_menu_treeview/camera_active_icon_red.svg" // Default fallback
                }
            }
        }

        // Camera Name
        Text {
            id: cameraNameText
            text: getCameraName()
            color: "white"
            font.pixelSize: 12
            font.bold: true
            anchors.verticalCenter: parent.verticalCenter

            // Text wrapping and eliding for long names
            width: Math.max(0, root.width - cameraStateIcon.width - parent.spacing - parent.anchors.margins * 2 - 16) // Leave some margin
            wrapMode: Text.Wrap
            elide: Text.ElideRight
            maximumLineCount: 1

            // // Background for better readability
            // Rectangle {
            //     anchors.fill: parent
            //     anchors.margins: -4
            //     color: "black"
            //     opacity: 0.8
            //     radius: 4
            //     z: -1
            // }

            function getCameraName() {
                if (!itemData || !itemData.name) return ""
                return itemData.name
            }
        }
    }
}


