import threading
import logging
from functools import partial
from typing import List

from PySide6.QtCore import Qt, QEvent, Signal, QPoint
from PySide6.QtGui import QAction, QGuiApplication, QIcon
from PySide6.QtWidgets import <PERSON><PERSON>abBar, Q<PERSON>abWidget, QVBoxLayout, QMenu, QWidget, QToolButton, QSizePolicy
from src.common.model.camera_model import camera_model_manager
from src.common.widget.camera_widget import CameraWidget
from src.common.model.device_models import TabType
from src.common.model.main_tree_view_model import TreeType
from src.common.widget.button_state import original_list_data_grid, GridButtonModel
from src.common.widget.menus.custom_menus import SubMenuOpenInTab
from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.presentation.camera_screen.camera_grid_widget import CameraGridWidget
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.utils.camera_qsettings import Camera_Qsettings
from src.common.joystick.joystick_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>,WidgetType
from src.common.key_board.key_board_manager import key_board_manager
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType
from src.common.key_board.shortcut_key import shortcut_key_model_manager
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.camera.video_capture import video_capture_controller
from src.presentation.camera_screen.managers.grid_manager import GridModel, gridManager
logger = logging.getLogger(__name__)


class NewCustomTabWidget(ActionableTitleBar):
    signal_add_tab_widget = Signal()
    signal_add_tab = Signal(object)
    close_tab_signal = Signal(object)
    close_all_tab_signal = Signal(object)

    def __init__(self, parent=None, window_parent=None):
        super(NewCustomTabWidget, self).__init__(parent)
        self.camera_screen = parent
        self.window_parent = window_parent
        # key_board_manager.set_func(start_key=Qt.Key.Key_Slash,func=self.process_shortcut_id)
        main_controller.list_parent['CustomTabWidget'] = self
        shortcut_key_model_manager.add_shortcut_key_list_signal.connect(self.add_shortcut_key_list_signal)
        shortcut_key_model_manager.add_shortcut_key_signal.connect(self.add_shortcut_key_signal)
        self.callback_current_changed = None
        screen = QGuiApplication.primaryScreen()
        desktop_screen_size = screen.availableGeometry()
        self.screen_available_width = desktop_screen_size.width()
        self.setup_ui()

    def setup_ui(self):
        self.tab_numbers = -1
        self.tab_widget = NewTabWidget(window_parent=self.window_parent)
        self.tab_widget.setFixedHeight(32)
        self.tab_widget.callback_load_menu = self.load_menu
        self.tab_widget.signal_add_new_tab.connect(self.__addTab)
        self.tab_widget.signal_tab_change.connect(self.__currentChanged)
        self.tab_widget.signal_tab_close.connect(self.tabClose)
        self.tab_widget.signal_close_all_tab.connect(self.close_all_tab)

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.layout.addWidget(self.tab_widget)

        self.setLayout(self.layout)

    def __addTab(self):
        # Show the corresponding content widget
        self.signal_add_tab_widget.emit()

    def load_menu(self, index):
        menu_list = {'new_view': None, 'open_view': None, 'add_to_virtual_window': None, 'add_to_savedview': None,
                     'save_as': None}
        # self.sub_menu = SubMenuOpenInTab()
        server_list = []
        for id,controller in controller_manager.data.items():
            server_list.append(controller.server.data.server_ip)

        widget = self.getWidgetByIndex(index)
        if hasattr(widget, 'gridModel'):
            from_gridModel = widget.gridModel
            from_gridModel.set_property("index", index)
            from_gridModel.saveCurrentData()
            # logger.debug(f'load_menu = {from_gridModel.get_property("index")} {from_gridModel.get_property('name')} {from_gridModel.get_property("type")}')
            if from_gridModel.get_property("type") == TabType.Invalid or from_gridModel.get_property("type") == TabType.SavedView or from_gridModel.get_property("type") == TabType.VirtualWindow:
                new_view = QAction(text=self.tr('New Tab'))
                new_view.triggered.connect(self.new_view_triggered)
                open_view_sub_menu = SubMenuOpenInTab()
                open_view = QAction(text=self.tr('Open Tab'), parent=open_view_sub_menu)
                open_view.setMenu(open_view_sub_menu)
                # open_view.triggered.connect(lambda: (self.open_view_triggered(name='')))

                # add danh sách tab không định danh
                for id, to_gridModel in gridManager.data.items():
                    if from_gridModel == to_gridModel:
                        if to_gridModel.get_property("type") == TabType.Invalid:
                            view = QAction(icon=QIcon(
                                Style.PrimaryImage.icon_active), text=self.shorten_text(to_gridModel.get_property('name')), parent=open_view_sub_menu)
                        else:
                            view = QAction(icon=QIcon(
                                Style.PrimaryImage.icon_active), text=f"{self.shorten_text(to_gridModel.get_property('name'))}\t{to_gridModel.get_property('server_ip')}", parent=open_view_sub_menu)
                    else:
                        if to_gridModel.get_property("type") == TabType.Invalid:
                            view = QAction(text=self.shorten_text(to_gridModel.get_property('name')), parent=open_view_sub_menu)
                        else:
                            view = QAction(text=f"{self.shorten_text(to_gridModel.get_property('name'))}\t{to_gridModel.get_property('server_ip')}", parent=open_view_sub_menu)
                    view.triggered.connect(
                        partial(self.view_triggered, to_gridModel = to_gridModel))
                    open_view_sub_menu.addAction(view)

                is_virtual_window_list = False
                add_to_virtual_sub_menu = SubMenuOpenInTab()
                for server_ip in server_list:
                    server_sub_menu = SubMenuOpenInTab()
                    for id, to_gridModel in gridManager.data.items():
                        if to_gridModel.get_property("type") == TabType.VirtualWindow and server_ip == to_gridModel.get_property('server_ip'):
                            # if tab_name == to_gridModel.get_property('name'):
                            #     add_to_view = QAction(icon=QIcon(
                            #         Style.PrimaryImage.icon_active), text=to_gridModel.get_property('name'), parent=server_sub_menu)
                            #     server_sub_menu.addAction(add_to_view)
                            # else:
                            add_to_view = QAction(text=to_gridModel.get_property('name'), parent=server_sub_menu)
                            server_sub_menu.addAction(add_to_view)

                            add_to_view.triggered.connect(
                                partial(self.add_to_view_triggered,from_gridModel = from_gridModel, to_gridModel = to_gridModel,from_widget = widget))
                            is_virtual_window_list = True
                    if is_virtual_window_list:
                        server = QAction(text=server_ip, parent=add_to_virtual_sub_menu)
                        server.setMenu(server_sub_menu)
                        add_to_virtual_sub_menu.addAction(server)
                add_to_virtual_window = QAction(text=self.tr('Add to Virtual Window'))
                add_to_virtual_window.setMenu(add_to_virtual_sub_menu)
                add_to_virtual_window.triggered.connect(self.add_to_virtual_window_triggered)


                add_to_savedview_sub_menu = SubMenuOpenInTab()
                add_to_savedview = QAction(text=self.tr('Add to Saved View'))
                add_to_savedview.setMenu(add_to_savedview_sub_menu)
                add_to_savedview.triggered.connect(self.add_to_savedview_triggered)
                is_savedview_list = False
                for server_ip in server_list:
                    server_sub_menu = SubMenuOpenInTab()
                    server = QAction(text=server_ip, parent=add_to_savedview_sub_menu)
                    server.setMenu(server_sub_menu)
                    for id, to_gridModel in gridManager.data.items():
                        if to_gridModel.get_property("type") == TabType.SavedView and server_ip == to_gridModel.get_property('server_ip'):
                            # if tab_name == to_gridModel.get_property('name'):
                            #     view = QAction(icon=QIcon(
                            #         Style.PrimaryImage.icon_active), text=to_gridModel.get_property('name'), parent=server_sub_menu)
                            # else:
                            view = QAction(text=to_gridModel.get_property('name'), parent=server_sub_menu)
                            view.triggered.connect(
                                partial(self.add_to_view_triggered, from_gridModel = from_gridModel,to_gridModel = to_gridModel,from_widget = widget))
                            server_sub_menu.addAction(view)
                            is_savedview_list = True
                    if is_savedview_list:
                        add_to_savedview_sub_menu.addAction(server)

                save_as_sub_menu = SubMenuOpenInTab()
                save_as = QAction(text=self.tr('Save as...'))
                save_as.setMenu(save_as_sub_menu)
                # save_as.triggered.connect(self.save_as_triggered)
                for server_ip in server_list:
                    server_sub_menu = SubMenuOpenInTab()
                    server = QAction(text=server_ip, parent=save_as_sub_menu)
                    server.setMenu(server_sub_menu)
                    save_as_virtual_window = QAction(text=self.tr('Virtual Window'), parent=save_as_sub_menu)
                    save_as_virtual_window.triggered.connect(partial(self.save_as_triggered,from_gridModel = from_gridModel,to_tab_type = TabType.VirtualWindow, to_tab_name = None,widget = widget,server_ip = server_ip))
                    save_as_saved_view = QAction(text=self.tr('Saved View'), parent=save_as_sub_menu)
                    save_as_saved_view.triggered.connect(partial(self.save_as_triggered,from_gridModel = from_gridModel,to_tab_type = TabType.SavedView, to_tab_name = None,widget = widget,server_ip = server_ip))
                    if from_gridModel.get_property("type") == TabType.Invalid:
                        server_sub_menu.addAction(save_as_virtual_window)
                        server_sub_menu.addAction(save_as_saved_view)
                        save_as_sub_menu.addAction(server)
                    elif from_gridModel.get_property("type") == TabType.SavedView:
                        server_sub_menu.addAction(save_as_virtual_window)
                        save_as_sub_menu.addAction(server)
                # save_as_sub_menu.addAction(save_as_virtual_window)
                # save_as_sub_menu.addAction(save_as_saved_view)
                if from_gridModel.get_property("type") == TabType.Invalid:
                    # save_as_sub_menu.addAction(save_as_virtual_window)
                    # save_as_sub_menu.addAction(save_as_saved_view)
                    menu_list['new_view'] = new_view
                    menu_list['open_view'] = open_view
                    if is_virtual_window_list:
                        menu_list['add_to_virtual_window'] = add_to_virtual_window
                    if is_savedview_list:
                        menu_list['add_to_savedview'] = add_to_savedview
                    menu_list['save_as'] = save_as
                elif from_gridModel.get_property("type") == TabType.SavedView:
                    # save_as_sub_menu.addAction(save_as_virtual_window)
                    menu_list['new_view'] = new_view
                    menu_list['open_view'] = open_view
                    if is_virtual_window_list:
                        menu_list['add_to_virtual_window'] = add_to_virtual_window
                    menu_list['save_as'] = save_as
                else:
                    menu_list['new_view'] = new_view
                    menu_list['open_view'] = open_view
                return menu_list
            # return (new_view, open_view, add_to_virtual_window,add_to_savedview,save_as)

        # self.tab_widget.load_menu()
    def save_as_triggered(self,from_gridModel = None,to_tab_type = None,to_tab_name = None,widget = None,server_ip = None):
        main_tree_view_widget = main_controller.list_parent['MainTreeView']
        main_tree_view_widget.save_as_triggered(from_gridModel = from_gridModel, to_tab_name = to_tab_name,to_tab_type = to_tab_type,widget = widget,server_ip = server_ip)

    def view_triggered(self, tab_name=None, to_gridModel = None):
        # logger.debug(f'view_triggered = {tab_name}')
        # Check xem view này đã mở lên chưa, nếu mở rồi thì switch sang tab đó thôi rồi return luôn
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            if hasattr(widget, 'gridModel'):
                widget_gridModel = widget.gridModel
            if to_gridModel == widget_gridModel:
                self.tab_widget.tab_bar.setCurrentIndex(i)
                # self.camera_screen.center_stacked_widget.setCurrentIndex(i)
                return


        if to_gridModel.get_property("type") == TabType.SavedView:
            main_tree_view_widget = main_controller.list_parent['MainTreeView']
            camera_screen = main_controller.list_parent['CameraScreen']
            item = main_tree_view_widget.get_item(name=to_gridModel.get_property('name'), tree_type=TreeType.Saved_View_Item,server_ip = to_gridModel.get_property('server_ip'))
            item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
            camera_screen.add_tab_widget(gridModel = to_gridModel)

        elif to_gridModel.get_property("type") == TabType.VirtualWindow:
            main_tree_view_widget = main_controller.list_parent['MainTreeView']
            item = main_tree_view_widget.get_item(name=to_gridModel.get_property('name'), tree_type=TreeType.Virtual_Window_Item,server_ip = to_gridModel.get_property('server_ip'))
            main_tree_view_widget.auto_open_virtual_window(item)

    def add_to_view_triggered(self,from_gridModel = None,to_gridModel = None,from_widget = None):
        # logger.debug(f'view_triggered = {from_tab_name} {to_tab_name}')
        main_tree_view_widget = main_controller.list_parent['MainTreeView']
        main_tree_view_widget.add_to_tab_triggered(from_gridModel = from_gridModel, to_gridModel = to_gridModel,from_widget = from_widget)

    def new_view_triggered(self):
        self.__addTab()

    def open_view_triggered(self, name=None):
        pass

    def add_to_savedview_triggered(self):
        pass

    def add_to_virtual_window_triggered(self):
        pass

    def find_name_selected(self, tab_type=TabType.VirtualWindow):
        name_list = []
        if tab_type == TabType.VirtualWindow:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == TabType.VirtualWindow:
                    name_list.append(gridModel.get_property("name",""))
            is_name_selected = False
            count = 0
            new_name = self.tr('Virtual Window ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('Virtual Window ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        elif tab_type == TabType.SavedView:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == TabType.SavedView:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name
        else:
            for id, gridModel in gridManager.data.items():
                if gridModel.get_property("type") == TabType.Invalid:
                    name_list.append(gridModel.get_property('name'))
            is_name_selected = False
            count = 0
            new_name = self.tr('View ') + f'{count}'
            while not is_name_selected:
                new_name = self.tr('View ') + f'{count}'
                if new_name not in name_list:
                    is_name_selected = True
                else:
                    count += 1
            return new_name

    def addTabWidget(self, gridModel = None):
        try:
            gridModel.saveSignalChanged.disconnect(self.saveSignalChanged)
        except TypeError:
            # Chưa từng connect trước đó
            pass
        gridModel.saveSignalChanged.connect(self.saveSignalChanged)
        self.tab_numbers += 1
        tab_num = self.tab_widget.qtab_widget.tabBar().count()
        main_controller.tab_count = tab_num
        self.tab_widget.tab_bar.insertTab(tab_num, gridModel.get_property('name'))
        self.tab_widget.tab_bar.setCurrentIndex(tab_num)
        gridModel.set_property("index",tab_num)

    def tabClose(self, count_tab_remain):
        logger.debug(f"tabClose = {count_tab_remain}")
        tab_name_temp = self.tab_widget.qtab_widget.tabBar().tabText(count_tab_remain)
        if '*' in tab_name_temp:
            tab_name = tab_name_temp.replace("*", "")
        else:
            tab_name = tab_name_temp

        # Get the widget being closed
        widget_to_close = self.camera_screen.center_stacked_widget.widget(count_tab_remain)

        # Collect camera IDs from the tab being closed
        cameras_in_closing_tab = set()
        if hasattr(widget_to_close, 'gridModel'):
            gridModel_to_close = widget_to_close.gridModel
            for position, camera_model in gridModel_to_close._active_cells.items():
                if camera_model and hasattr(camera_model, 'get_property'):
                    camera_id = camera_model.get_property('id')
                    if camera_id:
                        cameras_in_closing_tab.add(camera_id)

        # Check which cameras are used in other tabs
        cameras_in_other_tabs = set()
        for i in range(self.tab_widget.tab_bar.count()):
            if i != count_tab_remain:  # Skip the tab being closed
                widget = self.camera_screen.center_stacked_widget.widget(i)
                if hasattr(widget, 'gridModel'):
                    gridModel = widget.gridModel
                    for position, camera_model in gridModel._active_cells.items():
                        if camera_model and hasattr(camera_model, 'get_property'):
                            camera_id = camera_model.get_property('id')
                            if camera_id:
                                cameras_in_other_tabs.add(camera_id)

        # Determine which cameras should be completely unregistered
        cameras_to_unregister = cameras_in_closing_tab - cameras_in_other_tabs

        # Handle video capture cleanup with smart logic
        for camera_id in cameras_in_closing_tab:
            try:
                if camera_id in video_capture_controller._list_video_capture:
                    if camera_id in cameras_to_unregister:
                        # This camera is not used in other tabs - unregister completely
                        logger.debug(f"Last instance - unregistering camera: {camera_id}")
                        for stream_type, video_capture in list(video_capture_controller._list_video_capture[camera_id].items()):
                            try:
                                video_capture.stop_capture()
                            except Exception as e:
                                logger.error(f"Error stopping capture for camera {camera_id}, stream type {stream_type}: {e}")
                    else:
                        # This camera is used in other tabs - just remove share_frame_signal
                        logger.debug(f"Other tabs using camera - removing share_frame_signal for camera: {camera_id}")
                        # The actual share_frame_signal disconnection will be handled by CustomVideo.qml
                        # when it receives the videoInfoChanged signal with None camera_model
            except Exception as e:
                logger.error(f"Error handling video capture for camera {camera_id}: {e}")

        # Now proceed with normal tab closing logic
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            if i == count_tab_remain:
                if hasattr(widget, 'gridModel'):
                    gridModel:GridModel = widget.gridModel
                    # Use smart removal instead of removeAllItem
                    self._smart_remove_cameras(gridModel, cameras_to_unregister)

                    if gridModel.get_property("type") == TabType.VirtualWindow:
                        if gridModel.get_property('id') in main_controller.list_parent:
                            main_controller.list_parent[gridModel.get_property('id')][1].tab_closed = True
                            main_controller.list_parent[gridModel.get_property('id')][1].close()

                    elif gridModel.get_property("type") == TabType.SavedView:
                        main_treeview = main_controller.list_parent['MainTreeView']
                        savedview = main_treeview.get_item(name=tab_name, tree_type=TreeType.Saved_View_Item)
                        if savedview is not None:
                            savedview.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))

                    elif gridModel.get_property("type") == TabType.Invalid or TabType.FloorView:
                        gridManager.removeGridModel(gridModel)

                if widget is not None:
                    if isinstance(widget, CameraGridWidget):
                        widget.gridModel.set_property("isShow",False)
                    self.camera_screen.center_stacked_widget.removeWidget(
                        widget)  # Remove the widget from the QStackedWidget
                    widget.deleteLater()

        for index in range(self.camera_screen.center_stacked_widget.count()):
            widget = self.camera_screen.center_stacked_widget.widget(index)
            widget.gridModel.set_property("index",index)
        main_controller.tab_count = count_tab_remain
        # check if count tab remain is 0 then add new tab
        if count_tab_remain == 0 and (self.tab_widget.tab_bar.count() == 1 or self.tab_widget.tab_bar.count() == 0):
            # logger.debug(f'self.tab_widget.count() = {self.tab_widget.count()}')
            # add new tab
            self.tab_numbers = -1
            self.__addTab()

    def _smart_remove_cameras(self, gridModel, cameras_to_unregister):
        """Smart removal that only emits videoInfoChanged signal without calling stop_capture directly"""
        try:
            # Clear all active cells and emit signals for QML components to handle cleanup
            positions_to_remove = list(gridModel._active_cells.keys())

            for position in positions_to_remove:
                camera_model = gridModel._active_cells.get(position)
                if camera_model:
                    camera_id = camera_model.get_property('id') if hasattr(camera_model, 'get_property') else None

                    # Remove from active cells
                    gridModel._active_cells[position] = None

                    # Remove from cell dimensions
                    if position in gridModel._cell_dimensions:
                        del gridModel._cell_dimensions[position]

                    # Emit signal to notify QML components
                    # The QML components will handle the actual unregister/share_frame_signal logic
                    gridModel.videoInfoChanged.emit(position, None, False, False)

                    logger.debug(f"Removed camera {camera_id} from position {position}")

            # Clear the active cells dictionary
            gridModel._active_cells.clear()
            gridModel._cell_dimensions.clear()

            # Update grid layout with empty list since all cameras are removed
            gridModel.layoutChanged.emit([])

        except Exception as e:
            logger.error(f"Error in smart camera removal: {e}")
            # Fallback to original removeAllItem if smart removal fails
            gridModel.removeAllItem()

    def __currentChanged(self, idx):
        # logger.debug(f'__currentChanged: idx = {idx}')

        main_controller.current_tab = idx
        main_controller.current_tab_name = self.tab_widget.tab_bar.tabText(main_controller.current_tab)
        grid_item_selected.clear()
        # camera_grid_widget = self.getCurrentWidget()
        # # cấu hình camera lắng nghe joystick
        # # if self.grid_item_selected.is_tab_index(main_controller.current_tab) and self.grid_item_selected.data['tab_index'] is not None:
        # #     camera_widget = self.grid_item_selected.data['widget']
        # #     JoystickManager.get_instance().register(WidgetType.CameraScreen,camera_widget.key_received_signal)
        # # else:
        # #     JoystickManager.get_instance().unregister(widget_type=WidgetType.CameraScreen)
        # # # cấu hình hàm lắng nghe shortcut id
        # if hasattr(camera_grid_widget, 'process_shortcut_id'):
        #     KeyBoardManager.get_instance().set_func(start_key=Qt.Key.Key_Slash,func=camera_grid_widget.process_shortcut_id)
        # else:
        #     KeyBoardManager.get_instance().set_func(start_key=Qt.Key.Key_Slash,func=None)

        # dieu khien viec hien thi ui PTZWidget va cau hinh thuoc tinh ptz_onvif khi thay doi tab
        if self.callback_current_changed is not None:
            self.callback_current_changed(idx)
        # Cập nhạt trạng thái start Camera Stream cho tab hiện tại và stop camera stream cho các tab khác
        threading.Thread(target=self.update_current_tab_status,
                         args=(idx,)).start()
        widget = self.camera_screen.center_stacked_widget.currentWidget()
        # if widget is not None:
        #     widget.camera_bottom_toolbar.update_control_status(widget.tab_model)

    def add_shortcut_key_list_signal(self,data):
        start_key = int(Qt.Key.Key_Slash)
        if start_key in shortcut_key_model_manager.shortcut_key_list:
            shortcut_key_model_manager.shortcut_key_list[start_key]['func'] = self.process_shortcut_id

    def add_shortcut_key_signal(self,key):
        if key == int(Qt.Key.Key_Slash):
            shortcut_key_model_manager.shortcut_key_list[key]['func'] = self.process_shortcut_id

    def process_shortcut_id(self,id = None, tree_type = None):
        screen = grid_item_selected.data['screen']
        logger.debug(f"process_shortcut_id name = {id} - tree_type = {tree_type} - {grid_item_selected.data}")
        
        if screen is not None:
            # logger.debug(f"process_shortcut_id = {screen}")
            if screen == 'Main':
                camera_grid_widget = self.getCurrentWidget()
                if camera_grid_widget is not None and hasattr(camera_grid_widget, 'process_shortcut_id'):
                    camera_grid_widget.process_shortcut_id(id = id, tree_type = tree_type)
            else:
                data = main_controller.list_parent.get(screen, None)

                if data is not None:
                    virtual_window_widget = data[1]
                    if hasattr(virtual_window_widget, 'process_shortcut_id'):
                        virtual_window_widget.process_shortcut_id(id = id, tree_type = tree_type)

    def close_all_tab(self):
        logger.info(f"close_all_tab")

        # Collect all camera IDs from all tabs
        all_camera_ids = set()
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            if hasattr(widget, 'gridModel'):
                gridModel = widget.gridModel
                for position, camera_model in gridModel._active_cells.items():
                    if camera_model and hasattr(camera_model, 'get_property'):
                        camera_id = camera_model.get_property('id')
                        if camera_id:
                            all_camera_ids.add(camera_id)

        # Unregister all cameras since we're closing all tabs
        for camera_id in all_camera_ids:
            try:
                if camera_id in video_capture_controller._list_video_capture:
                    logger.debug(f"Closing all tabs - unregistering camera: {camera_id}")
                    for stream_type, video_capture in list(video_capture_controller._list_video_capture[camera_id].items()):
                        try:
                            video_capture.stop_capture()
                        except Exception as e:
                            logger.error(f"Error stopping capture for camera {camera_id}, stream type {stream_type}: {e}")
            except Exception as e:
                logger.error(f"Error handling video capture for camera {camera_id}: {e}")

        # Now close all tabs
        is_final_tab = False
        while not is_final_tab:
            count = self.tab_widget.tab_bar.count()
            if count == 1:
                is_final_tab = True
            self.tab_widget.removeTab(count - 1)

    def update_tab_name(self, old_tab_name, tab_name):
        # Get index by tab name
        widget = self.getWidgetByName(old_tab_name)
        tab_index = self.camera_screen.center_stacked_widget.indexOf(widget)
        self.tab_widget.rename_tab(tab_index, tab_name)

    def update_current_tab_status(self, idx):
        widget = self.camera_screen.center_stacked_widget.currentWidget()
        if widget is not None:
            # check widget.update_status_video_capture exist
            if hasattr(widget, 'update_status_video_capture'):
                widget.update_status_video_capture(status=True)
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            if i == idx:
                pass
            else:
                if hasattr(widget, 'update_status_camera_stream') and widget.gridModel.get_property("type") != TabType.VirtualWindow:
                    widget.update_status_camera_stream(
                        status=False, list_camera_ids=main_controller.list_camera_ids)

    def getCurrentWidget(self):
        widget = self.camera_screen.center_stacked_widget.widget(main_controller.current_tab)
        return widget

    def getWidgetByName(self, name):
        for i in range(self.tab_widget.tab_bar.count()):
            tab_name = self.tab_widget.tab_bar.tabText(i)
            if '*' in tab_name:
                new_tab_name = tab_name.replace("*", "")
            else:
                new_tab_name = tab_name
            if new_tab_name == name:
                return self.camera_screen.center_stacked_widget.widget(i)
        return None

    def getWidgetByGridModel(self, gridModel):
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            if widget.gridModel == gridModel:
                return widget
        return None

    def getWidgetByIndex(self, index):
        widget = self.camera_screen.center_stacked_widget.widget(index)
        return widget

    def getAllWidget(self) -> List:
        widgets = []
        for i in range(self.tab_widget.tab_bar.count()):
            widget = self.camera_screen.center_stacked_widget.widget(i)
            widgets.append(widget)
        return widgets

    def saveSignalChanged(self,gridModel, isSave):
        if not isSave:
            for i in range(self.tab_widget.tab_bar.count()):
                widget = self.camera_screen.center_stacked_widget.widget(i)
                if widget.gridModel == gridModel:
                    self.tab_widget.tab_bar.setTabText(i, f"{gridModel.get_property('name')}*")
                    break
        else:
            for i in range(self.tab_widget.tab_bar.count()):
                widget = self.camera_screen.center_stacked_widget.widget(i)
                if widget.gridModel == gridModel:
                    self.tab_widget.tab_bar.setTabText(i, gridModel.get_property('name'))
                    break

    def shorten_text(self, text, max_length=20):
        """Cắt văn bản nếu quá dài và thêm '...'"""
        return text if len(text) <= max_length else text[:max_length - 3] + "..."

    def restyle_custom_tab_widget(self):
        self.tab_widget.tab_bar.setStyleSheet(
            f"""
                QTabBar {{
                    qproperty-drawBase: 0;
                    background-color: transparent;
                    border: none
                }}
                QTabBar::pane {{
                    background: transparent;
                    border: none;

                }}
                QTabBar::pane#tab_bar_device_screen {{
                    background: transparent;
                    border: none;

                }}
                QTabBar::tab#tab_bar_custom {{
                    background-color: {main_controller.get_theme_attribute("Color", "tabbar_background_normal")};
                    color: {main_controller.get_theme_attribute("Color", "text_color_all_app")};
                    padding: 6px 12px;
                    font-weight: None;
                }}
                QTabBar::tab:selected#tab_bar_custom {{
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    background-color: {main_controller.get_theme_attribute("Color", "tabbar_background_selected")};
                    color: {main_controller.get_theme_attribute("Color", "tabbar_text_selected")};
                    font-weight: bold;
                }}
                QTabBar::close-button#tab_bar_custom {{
                    image: url({main_controller.get_theme_attribute("Image", "close_tab")});
                    subcontrol-position: right;
                }}
                QTabBar::close-button:hover#tab_bar_custom {{
                    image: url({main_controller.get_theme_attribute("Image", "close_tab")});
                    subcontrol-position: right;
                    background-color: {main_controller.get_theme_attribute("Color", "hover_button")};
                    border-radius: 2px;
                    border: None;
                    font-weight: None;
                }}
                QTabBar::close-button:selected#tab_bar_custom {{
                    image: url({Style.PrimaryImage.close_tab_white});
                    subcontrol-position: right;
                    background-color: {main_controller.get_theme_attribute("Color", "tabbar_background_selected")};
                }}
            """
        )

class CustomTabBar(QTabBar):
    def __init__(self, parent=None, window_parent=None, tab_widget=None):
        super().__init__(parent)
        self.initial_pos = None
        self.tab_widget = tab_widget
        self.installEventFilter(self)
        self.window_parent = window_parent
        self.setStyleSheet('''
            QTabBar::tab {
               background-color: #f0f0f0;
               border: 1px solid #ccc;
            }
            QTabBar::tab:selected {
               background-color: #ffffff;
               border-bottom-color: #ffffff;
            }
        '''
        )

    def _move(self):
        window_move = self.window_parent.window().windowHandle()
        window_move.startSystemMove()

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.initial_pos = event.position().toPoint()
        super().mousePressEvent(event)
        event.accept()

    def mouseMoveEvent(self, event):
        if self.initial_pos is not None:
            self._move()
        super().mouseMoveEvent(event)
        event.accept()

    def mouseReleaseEvent(self, event):
        self.initial_pos = None
        super().mouseReleaseEvent(event)
        event.accept()

    def eventFilter(self, watched, event):
        if watched == self:
            if event.type() == QEvent.Type.MouseButtonDblClick:
                mouse_button = event.button()
                if mouse_button == Qt.LeftButton:
                    # Toggle between full screen and normal mode
                    if self.window_parent.window().isMaximized():
                        self.window_parent.window().showNormal()
                    else:
                        self.window_parent.window().showMaximized()
            if event.type() == QEvent.Type.MouseButtonPress:
                mouse_button = event.button()
                if mouse_button == Qt.MouseButton.MiddleButton:
                    tab_idx = self.tab_widget.tabBar().tabAt(event.position().toPoint())
                    if tab_idx != -1:
                        # Close the tab
                        self.tab_widget.tabBar().tabCloseRequested.emit(tab_idx)
                        return True
        return super().eventFilter(watched, event)


class NewTabWidget(QWidget):
    signal_tab_change = Signal(object)
    signal_tab_close = Signal(int)
    signal_add_new_tab = Signal()
    signal_close_all_tab = Signal()

    def __init__(self, window_parent=None):
        super().__init__(window_parent)
        self.window_parent = window_parent
        self.__context_menu_p = 0
        self.callback_load_menu = None
        self.tab_name_list = set()
        self.background_color = Style.PrimaryColor.background
        self.on_background_color = Style.PrimaryColor.on_background
        self.text_selected_color = Style.PrimaryColor.white
        self.text_unselected_color = Style.PrimaryColor.text_unselected
        self.__initLastRemovedTabInfo()
        self.__initUi()

    def __initLastRemovedTabInfo(self):
        self.__last_removed_tab_idx = []
        self.__last_removed_tab_widget = []
        self.__last_removed_tab_title = []

    def __initUi(self):
        # Create a QTabWidget
        self.qtab_widget = QTabWidget()
        self.qtab_widget.setObjectName("tab_widget")
        self.qtab_widget.installEventFilter(self)
        self.qtab_widget.setMaximumHeight(self.qtab_widget.tabBar().height())
        self.qtab_widget.setTabBar(CustomTabBar(window_parent=self.window_parent, tab_widget=self.qtab_widget))
        self.qtab_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.qtab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.qtab_widget.tabBar().setExpanding(False)
        self.qtab_widget.setMovable(False)
        self.qtab_widget.setTabsClosable(True)
        # self.qtab_widget.setDocumentMode(False)
        self.qtab_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # set corner widget
        text_unselected_color = Style.PrimaryColor.text_unselected
        self.corner_button = QToolButton()
        # self.corner_btn set icon
        self.corner_button.setIcon(QIcon(Style.PrimaryImage.add_tab))
        # style for corner widget
        self.corner_button.setStyleSheet(
            f"""
                    QToolButton {{
                        background-color: {Style.PrimaryColor.primary};
                        color: {text_unselected_color};
                        padding: 8px 12px;
                        border-radius: 2px;
                        margin: 2px 2px 0px 2px;
                        border: 1px solid {Style.PrimaryColor.primary};
                    }}
                    QToolButton:hover {{
                        background-color: {Style.PrimaryColor.primary_hover};
                        border: 1px solid {Style.PrimaryColor.primary_hover};
                    }}
                    QToolButton:pressed {{
                        background-color: {Style.PrimaryColor.primary_pressed};
                        border: 1px solid {Style.PrimaryColor.primary_pressed};
                    }}

                """
        )
        self.qtab_widget.setCornerWidget(self.corner_button, Qt.Corner.TopLeftCorner)
        self.qtab_widget.customContextMenuRequested.connect(self.__prepareMenu)
        self.qtab_widget.setStyleSheet(
            f"""
            QTabWidget::pane {{
                background: transparent;
                border: none;
            }}

            QTabWidget::tab-bar {{
               border: 1px solid gray;
            }}
            """
        )
        # Connect the tabCloseRequested signal to a custom slot
        self.corner_button.clicked.connect(self.__addTab)
        self.tab_bar = self.qtab_widget.tabBar()

        self.tab_bar.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.tab_bar.setMinimumWidth(300)
        self.tab_bar.setObjectName("tab_bar_custom")
        self.tab_bar.currentChanged.connect(self.onTabChange)
        self.tab_bar.tabCloseRequested.connect(self.removeTab)

        layout_tab_widget = QVBoxLayout()
        layout_tab_widget.setContentsMargins(0, 0, 0, 0)
        layout_tab_widget.setAlignment(Qt.AlignmentFlag.AlignBottom)
        layout_tab_widget.setSpacing(0)
        layout_tab_widget.addWidget(self.qtab_widget)
        self.setLayout(layout_tab_widget)

    def __addTab(self):
        # new_tab_index = self.qtab_widget.tabBar().count()
        # new_tab_name = f"View {new_tab_index}"
        # self.qtab_widget.tabBar().insertTab(new_tab_index, new_tab_name)
        # # Activate the newly added tab
        # self.tab_bar.setCurrentIndex(new_tab_index)

        self.signal_add_new_tab.emit()

    def __prepareMenu(self, pos):
        # Get the position of the context menu relative to the QTabWidget
        global_pos = self.qtab_widget.mapToGlobal(pos)
        # Get the position of the context menu relative to the QTabBar
        tab_bar = self.qtab_widget.tabBar()
        tab_bar_pos = tab_bar.mapFromGlobal(global_pos)
        # Retrieve the tab index at the calculated position
        tab_idx = tab_bar.tabAt(tab_bar_pos)
        if tab_idx != -1 and tab_idx == self.tab_bar.currentIndex():
            self.__context_menu_p = tab_bar_pos
            # closeTabAction = QAction('Close Tab')
            # closeTabAction.triggered.connect(self.closeTab)
            #
            # closeAllTabAction = QAction('Close All Tabs')
            # closeAllTabAction.triggered.connect(self.closeAllTab)
            #
            # closeOtherTabAction = QAction('Close Other Tabs')
            # closeOtherTabAction.triggered.connect(self.closeOtherTab)
            #
            # closeTabToTheLeftAction = QAction('Close Tabs to the Left')
            # closeTabToTheLeftAction.triggered.connect(self.closeTabToLeft)
            #
            # closeTabToTheRightAction = QAction('Close Tabs to the Right')
            # closeTabToTheRightAction.triggered.connect(self.closeTabToRight)
            #
            # reopenClosedTabAction = QAction('Reopen Closed Tab')
            # # reopenClosedTabAction.triggered.connect(self.reopenClosedTab)
            #
            # menu = QMenu(self)
            # menu.addAction(closeTabAction)
            # menu.addAction(closeAllTabAction)
            # menu.addAction(closeOtherTabAction)
            # menu.addAction(closeTabToTheLeftAction)
            # menu.addAction(closeTabToTheRightAction)
            # menu.addAction(reopenClosedTabAction)
            self.menu = QMenu(self)
            self.menu.setWindowFlags(
                Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
            self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
            self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
            logger.critical(f'call callback_load_menu = {tab_idx}')
            menu_list = self.callback_load_menu(tab_idx)
            if menu_list['new_view']:
                self.menu.addAction(menu_list['new_view'])
            if menu_list['open_view']:
                self.menu.addAction(menu_list['open_view'])
            self.menu.addSeparator()
            if menu_list['add_to_virtual_window']:
                self.menu.addAction(menu_list['add_to_virtual_window'])
            if menu_list['add_to_savedview']:
                self.menu.addAction(menu_list['add_to_savedview'])
            if menu_list['save_as']:
                self.menu.addAction(menu_list['save_as'])
            self.menu.addSeparator()
            # self.load_menu_signal.emit(tab_idx)
            self.closeTabAction = QAction(self.tr('Close Tab'))
            self.closeTabAction.triggered.connect(self.closeTab)

            self.closeAllTabAction = QAction(self.tr('Close All Tabs'))
            self.closeAllTabAction.triggered.connect(self.closeAllTab)
            self.menu.addAction(self.closeTabAction)
            self.menu.addAction(self.closeAllTabAction)
            self.menu.exec(self.mapToGlobal(tab_bar_pos))

    def setCurrentIndex(self, index):
        self.qtab_widget.tabBar().setCurrentIndex(index)

    def count(self):
        return self.qtab_widget.tabBar().count()

    def tabText(self, index):
        return self.qtab_widget.tabBar().tabText(index)

    def removeTab(self, index):
        self.signal_tab_close.emit(index)
        # Get the tab name from the tab bar
        tab_name = self.tab_bar.tabText(index)
        # Close the tab in the tab bar
        # self.__saveLastRemovedTabInfo(index)
        self.tab_bar.removeTab(index)

    def closeTab(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.qtab_widget.tabBar().tabAt(self.__context_menu_p)
            self.removeTab(tab_idx)
            self.__context_menu_p = 0
        else:
            self.removeTab(self.tab_bar.currentIndex())

    def closeAllTab(self):
        self.signal_close_all_tab.emit()

        self.tab_name_list.clear()

    def closeOtherTab(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tab_bar.tabAt(self.__context_menu_p)
            self.__removeTabFromLeftTo(tab_idx)
            tab_idx = 0
            self.tab_bar.setCurrentIndex(tab_idx)
            self.__removeTabFromRightTo(tab_idx)

    def closeTabToLeft(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tab_bar.tabAt(self.__context_menu_p)
            self.__removeTabFromLeftTo(tab_idx)
        self.signal_tab_close.emit(self.tab_bar.count())

    def __removeTabFromLeftTo(self, idx):
        for i in range(idx - 1, -1, -1):
            self.removeTab(i)

    def __removeTabFromRightTo(self, idx):
        for i in range(self.tab_bar.count() - 1, idx, -1):
            self.removeTab(i)

    def closeTabToRight(self):
        if isinstance(self.__context_menu_p, QPoint):
            tab_idx = self.tab_bar.tabAt(self.__context_menu_p)
            self.__removeTabFromRightTo(tab_idx)
        self.signal_tab_close.emit(self.tab_bar.count())

    def __saveLastRemovedTabInfo(self, idx):
        self.__last_removed_tab_idx.append(idx)
        # self.__last_removed_tab_widget.append(self.widget(idx))
        self.__last_removed_tab_title.append(self.tab_bar.tabText(idx))

    def onTabChange(self, index):
        self.signal_tab_change.emit(index)
        # pass

    def rename_tab(self, index, title):
        self.tab_bar.setTabText(index, title)

    def generate_unique_tab_name(self):
        # Generate a unique tab name
        tab_name_base = "Tab"
        count = 1
        while True:
            tab_name = f"{tab_name_base} {count}"
            if tab_name not in self.tab_name_list:
                break
            count += 1
        # Add the tab name to the set of existing names
        self.tab_name_list.add(tab_name)
        return tab_name

    def eventFilter(self, watched, e):
        if watched.objectName() == "tab_widget":
            if e.type() == QEvent.Type.MouseButtonPress:
                mouse_button = e.button()
                if mouse_button == Qt.MouseButton.MiddleButton:
                    global_pos = self.qtab_widget.mapToGlobal(e.position())
                    # Get the position of the context menu relative to the QTabBar
                    tab_bar = self.qtab_widget.tabBar()
                    tab_bar_pos = tab_bar.mapFromGlobal(global_pos)
                    # Retrieve the tab index at the calculated position
                    tab_idx = tab_bar.tabAt(tab_bar_pos.toPoint())
                    if tab_idx != -1:
                        # Close the tab
                        self.tab_bar.tabCloseRequested.emit(tab_idx)
                        return True
            # if e.modifiers() & Qt.AltModifier and e.key() == Qt.Key_Left:
            #     self.tab_widget.tabBar().setCurrentIndex(self.tab_widget.tabBar().currentIndex() - 1)
            # elif e.modifiers() & Qt.AltModifier and e.key() == Qt.Key_Right:
            #     self.tab_widget.tabBar().setCurrentIndex(self.tab_widget.tabBar().currentIndex() + 1)
            # elif e.modifiers() & Qt.ControlModifier and e.key() == Qt.Key_F4:
            #     self.closeTab()

        return super().eventFilter(watched, e)
